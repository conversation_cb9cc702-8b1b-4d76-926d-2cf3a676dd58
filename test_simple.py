"""
Prueba simple del servidor
"""

import requests
import json

def test_server():
    """Probar que el servidor esté funcionando"""
    try:
        print("🔍 Probando servidor...")
        
        # Probar endpoint principal
        response = requests.get("http://localhost:8000/")
        print(f"✅ Servidor responde: {response.status_code}")
        print(f"📄 Respuesta: {json.dumps(response.json(), indent=2)}")
        
        # Probar endpoint de salud
        response = requests.get("http://localhost:8000/health")
        print(f"✅ Health check: {response.status_code}")
        print(f"📄 Estado: {json.dumps(response.json(), indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_server()
