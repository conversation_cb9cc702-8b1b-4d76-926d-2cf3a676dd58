"""
Entrenamiento de YOLO personalizado
"""

import os
import yaml
import json
from ultralytics import YOLO
import shutil
from datetime import datetime
import matplotlib.pyplot as plt

class YOLOTrainer:
    def __init__(self):
        self.model = None
        self.dataset_path = None
        self.results = None
        
    def list_available_datasets(self):
        """Listar datasets disponibles"""
        datasets_dir = "datasets"
        if not os.path.exists(datasets_dir):
            print("❌ No hay datasets disponibles")
            return []
        
        available = []
        for item in os.listdir(datasets_dir):
            dataset_path = os.path.join(datasets_dir, item)
            if os.path.isdir(dataset_path):
                config_path = os.path.join(dataset_path, "config.json")
                yaml_path = os.path.join(dataset_path, "dataset.yaml")
                
                if os.path.exists(config_path) and os.path.exists(yaml_path):
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                    available.append({
                        'name': item,
                        'path': dataset_path,
                        'config': config
                    })
        
        return available
    
    def select_dataset(self):
        """Seleccionar dataset para entrenar"""
        datasets = self.list_available_datasets()
        
        if not datasets:
            print("❌ No hay datasets disponibles")
            print("💡 Primero crea un dataset con: python create_custom_dataset.py")
            return False
        
        print("\n📋 Datasets disponibles:")
        for i, dataset in enumerate(datasets):
            config = dataset['config']
            print(f"{i+1}. {dataset['name']}")
            print(f"   📅 Creado: {config.get('created_at', 'N/A')}")
            print(f"   🏷️ Clases: {', '.join(config.get('classes', []))}")
            print(f"   📊 Total clases: {config.get('total_classes', 0)}")
            
            # Contar imágenes
            train_dir = os.path.join(dataset['path'], 'images', 'train')
            val_dir = os.path.join(dataset['path'], 'images', 'val')
            train_count = len([f for f in os.listdir(train_dir) if f.endswith(('.jpg', '.png'))]) if os.path.exists(train_dir) else 0
            val_count = len([f for f in os.listdir(val_dir) if f.endswith(('.jpg', '.png'))]) if os.path.exists(val_dir) else 0
            
            print(f"   📸 Imágenes: {train_count} train, {val_count} val")
            print()
        
        try:
            choice = int(input(f"Selecciona dataset (1-{len(datasets)}): ")) - 1
            if 0 <= choice < len(datasets):
                self.dataset_path = os.path.join(datasets[choice]['path'], 'dataset.yaml')
                print(f"✅ Dataset seleccionado: {datasets[choice]['name']}")
                return True
            else:
                print("❌ Selección inválida")
                return False
        except ValueError:
            print("❌ Entrada inválida")
            return False
    
    def check_dataset_ready(self):
        """Verificar que el dataset esté listo para entrenar"""
        if not self.dataset_path:
            return False
        
        # Cargar configuración YAML
        with open(self.dataset_path, 'r') as f:
            config = yaml.safe_load(f)
        
        dataset_dir = os.path.dirname(self.dataset_path)
        
        # Verificar directorios
        required_dirs = ['images/train', 'images/val', 'labels/train', 'labels/val']
        missing_dirs = []
        
        for req_dir in required_dirs:
            full_path = os.path.join(dataset_dir, req_dir)
            if not os.path.exists(full_path):
                missing_dirs.append(req_dir)
        
        if missing_dirs:
            print(f"❌ Faltan directorios: {missing_dirs}")
            return False
        
        # Contar archivos
        train_images = len([f for f in os.listdir(os.path.join(dataset_dir, 'images/train')) 
                           if f.endswith(('.jpg', '.png', '.jpeg'))])
        train_labels = len([f for f in os.listdir(os.path.join(dataset_dir, 'labels/train')) 
                           if f.endswith('.txt')])
        val_images = len([f for f in os.listdir(os.path.join(dataset_dir, 'images/val')) 
                         if f.endswith(('.jpg', '.png', '.jpeg'))])
        val_labels = len([f for f in os.listdir(os.path.join(dataset_dir, 'labels/val')) 
                         if f.endswith('.txt')])
        
        print(f"\n📊 Estado del dataset:")
        print(f"🏷️ Clases: {config.get('nc', 0)} ({config.get('names', [])})")
        print(f"📸 Imágenes train: {train_images}")
        print(f"🏷️ Labels train: {train_labels}")
        print(f"📸 Imágenes val: {val_images}")
        print(f"🏷️ Labels val: {val_labels}")
        
        if train_images == 0:
            print("❌ No hay imágenes de entrenamiento")
            return False
        
        if train_labels == 0:
            print("❌ No hay etiquetas de entrenamiento")
            print("💡 Necesitas anotar las imágenes primero")
            return False
        
        if train_images != train_labels:
            print("⚠️ Advertencia: Número de imágenes y etiquetas no coincide")
        
        if val_images == 0:
            print("⚠️ Advertencia: No hay imágenes de validación")
            print("💡 Se recomienda tener al menos 20% para validación")
        
        return True
    
    def train_model(self, epochs=100, model_size='n', batch_size=16, img_size=640):
        """Entrenar modelo YOLO personalizado"""
        if not self.check_dataset_ready():
            print("❌ Dataset no está listo para entrenar")
            return False
        
        print(f"\n🚀 Iniciando entrenamiento YOLO personalizado...")
        print(f"📊 Configuración:")
        print(f"   🤖 Modelo: YOLOv8{model_size}")
        print(f"   📈 Épocas: {epochs}")
        print(f"   📦 Batch size: {batch_size}")
        print(f"   📐 Tamaño imagen: {img_size}")
        print(f"   📁 Dataset: {self.dataset_path}")
        
        try:
            # Cargar modelo base
            self.model = YOLO(f'yolov8{model_size}.pt')
            
            # Crear directorio de resultados
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_name = f"custom_training_{timestamp}"
            
            print(f"\n🎯 Entrenando... (esto puede tardar varios minutos)")
            print("💡 Puedes ver el progreso en la consola")
            
            # Entrenar
            self.results = self.model.train(
                data=self.dataset_path,
                epochs=epochs,
                batch=batch_size,
                imgsz=img_size,
                project='runs/train',
                name=project_name,
                device='cpu',  # Cambiar a 'cuda' si tienes GPU
                save_period=10,  # Guardar cada 10 épocas
                patience=20,     # Early stopping
                augment=True,    # Data augmentation
                verbose=True,
                plots=True       # Generar gráficos
            )
            
            print(f"\n✅ Entrenamiento completado!")
            print(f"📁 Resultados guardados en: runs/train/{project_name}")
            
            # Mostrar métricas finales
            if hasattr(self.results, 'results_dict'):
                metrics = self.results.results_dict
                print(f"\n📊 Métricas finales:")
                for key, value in metrics.items():
                    if isinstance(value, (int, float)):
                        print(f"   {key}: {value:.4f}")
            
            # Ruta del mejor modelo
            best_model_path = f"runs/train/{project_name}/weights/best.pt"
            print(f"\n🏆 Mejor modelo: {best_model_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error durante entrenamiento: {e}")
            return False
    
    def test_trained_model(self, model_path, test_image=None):
        """Probar modelo entrenado"""
        if not os.path.exists(model_path):
            print(f"❌ Modelo no encontrado: {model_path}")
            return
        
        print(f"🧪 Probando modelo: {model_path}")
        
        # Cargar modelo entrenado
        model = YOLO(model_path)
        
        if test_image and os.path.exists(test_image):
            # Probar con imagen específica
            results = model(test_image)
            results[0].show()  # Mostrar resultado
            print(f"✅ Resultado mostrado para: {test_image}")
        else:
            # Probar con cámara
            print("📹 Probando con cámara en tiempo real...")
            print("❌ Presiona 'q' para salir")
            
            import cv2
            cap = cv2.VideoCapture(0)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame = cv2.flip(frame, 1)
                results = model(frame, conf=0.3, verbose=False)
                
                # Dibujar detecciones
                if results[0].boxes is not None:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    confidences = results[0].boxes.conf.cpu().numpy()
                    classes = results[0].boxes.cls.cpu().numpy()
                    
                    for box, conf, cls in zip(boxes, confidences, classes):
                        x1, y1, x2, y2 = map(int, box)
                        class_name = model.names[int(cls)]
                        
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(frame, f"{class_name}: {conf:.2f}", (x1, y1-10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                cv2.putText(frame, "Modelo Personalizado - Presiona 'q' para salir", 
                          (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                cv2.imshow('YOLO Personalizado', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()

def main():
    print("🎯 Entrenador de YOLO Personalizado")
    print("=" * 50)
    
    trainer = YOLOTrainer()
    
    while True:
        print("\n📋 Opciones:")
        print("1. 📊 Ver datasets disponibles")
        print("2. 🚀 Entrenar modelo personalizado")
        print("3. 🧪 Probar modelo entrenado")
        print("4. ❌ Salir")
        
        choice = input("\nElige una opción (1-4): ").strip()
        
        if choice == '1':
            datasets = trainer.list_available_datasets()
            if not datasets:
                print("❌ No hay datasets disponibles")
                print("💡 Crea uno con: python create_custom_dataset.py")
        
        elif choice == '2':
            if trainer.select_dataset():
                print("\n⚙️ Configuración de entrenamiento:")
                
                try:
                    epochs = int(input("Épocas (default 100): ") or "100")
                    model_size = input("Tamaño modelo [n/s/m/l/x] (default n): ") or "n"
                    batch_size = int(input("Batch size (default 16): ") or "16")
                    img_size = int(input("Tamaño imagen (default 640): ") or "640")
                    
                    trainer.train_model(epochs, model_size, batch_size, img_size)
                    
                except ValueError:
                    print("❌ Valores inválidos")
        
        elif choice == '3':
            model_path = input("Ruta del modelo (.pt): ").strip()
            test_image = input("Imagen de prueba (opcional): ").strip() or None
            trainer.test_trained_model(model_path, test_image)
        
        elif choice == '4':
            print("👋 ¡Hasta luego!")
            break
        
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
