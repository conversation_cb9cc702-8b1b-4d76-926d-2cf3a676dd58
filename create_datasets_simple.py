"""
Crear datasets de forma simple y directa
"""

import os
import json
from datetime import datetime

# Lista de objetos importantes
objects_list = [
    "personas_con_armas",
    "armas_de_fuego", 
    "cuchillos",
    "tren",
    "autos",
    "motos", 
    "bicicletas",
    "camiones",
    "plantas",
    "arboles",
    "flores",
    "personas",
    "rostros",
    "manos",
    "comida",
    "bebidas",
    "libros",
    "herramientas",
    "computadoras",
    "televisores",
    "camaras",
    "muebles",
    "ropa",
    "medicamentos",
    "jeringas",
    "documentos_identidad",
    "billetes",
    "tarjetas",
    "pelotas_deportivas",
    "fuego",
    "humo",
    "accidentes"
]

def create_all_datasets():
    """Crear todos los datasets"""
    print("🚀 Creando datasets masivos...")
    print("=" * 50)
    
    created = 0
    failed = 0
    
    for obj in objects_list:
        try:
            # Crear estructura de directorios
            dataset_name = f"manual_{obj}"
            base_path = f"datasets/{dataset_name}"
            
            directories = [
                f"{base_path}/raw_images",
                f"{base_path}/images/train", 
                f"{base_path}/images/val",
                f"{base_path}/images/test",
                f"{base_path}/labels/train",
                f"{base_path}/labels/val", 
                f"{base_path}/labels/test"
            ]
            
            # Crear directorios
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
            
            # Crear configuración
            config = {
                "object_name": obj,
                "dataset_name": dataset_name,
                "method": "manual",
                "created_at": datetime.now().isoformat(),
                "status": "ready_for_images",
                "target_images": "50-100"
            }
            
            with open(f"{base_path}/config.json", 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f"✅ {obj}")
            created += 1
            
        except Exception as e:
            print(f"❌ {obj}: {e}")
            failed += 1
    
    print("=" * 50)
    print(f"📊 RESUMEN:")
    print(f"✅ Creados: {created}")
    print(f"❌ Fallaron: {failed}")
    print(f"📁 Ubicación: datasets/")
    
    # Crear archivo de instrucciones
    instructions = f"""# Datasets Creados - {datetime.now().strftime('%Y-%m-%d %H:%M')}

## 📊 Total: {created} datasets creados

## 📋 Lista de Datasets:
"""
    
    for obj in objects_list:
        instructions += f"- manual_{obj}/raw_images/ ← Agrega imágenes de {obj}\n"
    
    instructions += """

## 🎯 Instrucciones de Uso:

### Para cada dataset:
1. 📁 Ve a: datasets/manual_[objeto]/raw_images/
2. 📸 Agrega 50-100 imágenes del objeto
3. 🌐 Ve a: https://roboflow.com/
4. 📤 Sube las imágenes
5. 🏷️ Anota cada objeto con bounding boxes
6. 📊 Divide: 70% train, 20% val, 10% test
7. 📥 Exporta en formato 'YOLOv8'
8. 📂 Descarga y extrae en la carpeta del dataset
9. 🤖 Entrena: python train_custom_yolo.py

## 💡 Fuentes de Imágenes:
- 📱 Fotos con tu teléfono
- 🌐 Google Images
- 📹 Capturas de video
- 🎥 Frames de películas
- 📷 Unsplash, Pixabay

## 🎯 Consejos Específicos:

### Seguridad:
- 🔫 **Armas**: Diferentes tipos, en manos, sobre mesa
- 👤 **Personas con armas**: Diferentes poses, uniformes

### Transporte:
- 🚗 **Autos**: Diferentes marcas, colores, ángulos
- 🏍️ **Motos**: Deportivas, cruiser, scooters
- 🚂 **Trenes**: Pasajeros, carga, metro

### Naturaleza:
- 🌱 **Plantas**: Interior, exterior, diferentes especies
- 🌳 **Árboles**: Diferentes tipos, estaciones

### Personas:
- 👤 **Personas**: Diferentes edades, ropa, poses
- 😊 **Rostros**: Diferentes expresiones, ángulos
- ✋ **Manos**: Diferentes gestos, objetos

### Documentos:
- 🆔 **Documentos**: Cédulas, pasaportes, licencias
- 💵 **Billetes**: Diferentes denominaciones
- 💳 **Tarjetas**: Crédito, débito, identificación

### Emergencias:
- 🔥 **Fuego**: Incendios, fogatas, diferentes intensidades
- 💨 **Humo**: Diferentes densidades, colores
- 🚨 **Accidentes**: Vehículos, personas, diferentes tipos

## 🎉 Resultado Esperado:
- 🎯 Detección 95%+ precisa para cada objeto
- ❌ Menos falsos positivos
- 🚀 Modelos personalizados optimizados
"""
    
    with open("datasets/INSTRUCCIONES.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"📖 Instrucciones guardadas en: datasets/INSTRUCCIONES.md")
    print(f"🎯 ¡Listo para agregar imágenes!")

if __name__ == "__main__":
    create_all_datasets()
