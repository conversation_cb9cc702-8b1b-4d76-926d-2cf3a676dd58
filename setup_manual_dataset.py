"""
Configurador de dataset manual - Método más fácil
Solo crea las carpetas, tú agregas las imágenes manualmente
"""

import os
import json
from datetime import datetime

def create_manual_dataset_structure():
    """Crear estructura de dataset para método manual"""
    print("📁 Configurador de Dataset Manual")
    print("=" * 50)
    print("🎯 Este método es MÁS FÁCIL y RÁPIDO:")
    print("1. 📂 Creamos las carpetas")
    print("2. 📸 Tú agregas imágenes manualmente")
    print("3. 🏷️ Anotas en Roboflow")
    print("4. 🤖 Entrenas el modelo")
    
    # Obtener nombre del objeto
    print("\n📋 Objetos populares:")
    print("💊 pastillas, 📱 telefono, 💻 laptop, 🍼 botella")
    print("📄 documento, 🔧 herramienta, 🎮 control, 🔑 llave")
    
    object_name = input("\n🏷️ Nombre del objeto: ").strip().lower()
    
    if not object_name:
        print("❌ Nombre requerido")
        return
    
    # Crear estructura
    dataset_name = f"manual_{object_name}"
    base_path = f"datasets/{dataset_name}"
    
    # Directorios a crear
    directories = [
        f"{base_path}/raw_images",
        f"{base_path}/images/train", 
        f"{base_path}/images/val",
        f"{base_path}/images/test",
        f"{base_path}/labels/train",
        f"{base_path}/labels/val", 
        f"{base_path}/labels/test"
    ]
    
    # Crear directorios
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Creado: {directory}")
    
    # Crear archivo de configuración
    config = {
        "object_name": object_name,
        "dataset_name": dataset_name,
        "method": "manual",
        "created_at": datetime.now().isoformat(),
        "instructions": {
            "step_1": f"Agrega 50-100 imágenes de {object_name} en raw_images/",
            "step_2": "Sube a Roboflow.com para anotar",
            "step_3": "Descarga dataset anotado",
            "step_4": "Ejecuta train_custom_yolo.py"
        }
    }
    
    config_path = f"{base_path}/config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # Crear archivo README
    readme_content = f"""# Dataset Manual: {object_name.title()}

## 📁 Estructura Creada:
```
{dataset_name}/
├── raw_images/          ← AGREGA TUS IMÁGENES AQUÍ
├── images/
│   ├── train/          ← Imágenes de entrenamiento (después de anotar)
│   ├── val/            ← Imágenes de validación
│   └── test/           ← Imágenes de prueba
└── labels/
    ├── train/          ← Etiquetas de entrenamiento
    ├── val/            ← Etiquetas de validación
    └── test/           ← Etiquetas de prueba
```

## 🎯 Pasos a Seguir:

### 1. 📸 Agregar Imágenes (MANUAL)
- Copia 50-100 imágenes de {object_name} en `raw_images/`
- Usa fotos de tu teléfono, internet, o toma nuevas
- Diferentes ángulos, iluminación, fondos

### 2. 🏷️ Anotar en Roboflow
1. Ve a: https://roboflow.com/
2. Crea cuenta gratuita
3. Sube todas las imágenes de `raw_images/`
4. Anota cada {object_name} con bounding boxes
5. Divide: 70% train, 20% val, 10% test
6. Exporta en formato "YOLOv8"

### 3. 📥 Descargar Dataset Anotado
- Descarga el ZIP de Roboflow
- Extrae en la carpeta del dataset
- Debe crear automáticamente images/ y labels/

### 4. 🤖 Entrenar Modelo
```bash
python train_custom_yolo.py
```

## 💡 Consejos para {object_name.title()}:
"""

    if "pastilla" in object_name or "medicamento" in object_name:
        readme_content += """
- 💊 Pastillas individuales y en grupos
- 📦 En blister y sueltas
- 🎨 Diferentes colores y formas
- 📏 Diferentes tamaños
- ✋ En mano y sobre superficies
"""
    elif "telefono" in object_name or "celular" in object_name:
        readme_content += """
- 📱 Pantalla encendida y apagada
- 🔄 Vertical y horizontal
- ✋ En mano y sobre mesa
- 📞 Diferentes modelos
- 🔌 Con y sin cables
"""
    elif "laptop" in object_name or "computadora" in object_name:
        readme_content += """
- 💻 Abierta y cerrada
- 💡 Pantalla encendida/apagada
- 🔌 Con y sin cables
- 📐 Diferentes ángulos
- 🏠 Diferentes ubicaciones
"""
    else:
        readme_content += f"""
- 🎯 {object_name.title()} desde diferentes ángulos
- 💡 Diferentes condiciones de luz
- 🎨 Diferentes fondos
- 📏 Diferentes distancias
- 🔄 Diferentes orientaciones
"""

    readme_content += f"""

## 🎉 Resultado Esperado:
- 🎯 Detección 95%+ precisa de {object_name}
- ❌ Menos falsos positivos
- 🚀 Modelo personalizado optimizado
"""

    readme_path = f"{base_path}/README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"\n✅ Dataset manual configurado: {dataset_name}")
    print(f"📁 Ubicación: {base_path}")
    print(f"📖 Instrucciones: {readme_path}")
    
    print(f"\n🎯 PRÓXIMO PASO:")
    print(f"📸 Agrega 50-100 imágenes de {object_name} en:")
    print(f"📁 {base_path}/raw_images/")
    
    print(f"\n💡 FUENTES DE IMÁGENES:")
    print("1. 📱 Fotos con tu teléfono")
    print("2. 🌐 Descargar de Google Images")
    print("3. 📷 Tomar fotos específicas")
    print("4. 🎥 Capturas de video")
    
    print(f"\n📋 DESPUÉS DE AGREGAR IMÁGENES:")
    print("1. 🌐 Ve a Roboflow.com")
    print("2. 📤 Sube las imágenes")
    print("3. 🏷️ Anota los objetos")
    print("4. 📥 Descarga dataset")
    print("5. 🤖 Entrena con: python train_custom_yolo.py")
    
    return base_path

def show_existing_datasets():
    """Mostrar datasets existentes"""
    datasets_dir = "datasets"
    if not os.path.exists(datasets_dir):
        print("📂 No hay datasets creados aún")
        return
    
    print("\n📋 Datasets Existentes:")
    print("=" * 30)
    
    for item in os.listdir(datasets_dir):
        dataset_path = os.path.join(datasets_dir, item)
        if os.path.isdir(dataset_path):
            config_path = os.path.join(dataset_path, "config.json")
            
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                
                object_name = config.get('object_name', 'unknown')
                method = config.get('method', 'unknown')
                created = config.get('created_at', 'unknown')
                
                # Contar imágenes
                raw_images_dir = os.path.join(dataset_path, 'raw_images')
                image_count = 0
                if os.path.exists(raw_images_dir):
                    image_count = len([f for f in os.listdir(raw_images_dir) 
                                     if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                
                print(f"📁 {item}")
                print(f"   🏷️ Objeto: {object_name}")
                print(f"   📊 Método: {method}")
                print(f"   📸 Imágenes: {image_count}")
                print(f"   📅 Creado: {created[:10]}")
                print()

def main():
    """Función principal"""
    print("🚀 Configurador de Dataset Manual")
    print("Método más fácil para crear datasets personalizados")
    
    while True:
        print("\n📋 Opciones:")
        print("1. 🆕 Crear nuevo dataset manual")
        print("2. 📋 Ver datasets existentes") 
        print("3. ❌ Salir")
        
        choice = input("\nElige opción (1-3): ").strip()
        
        if choice == '1':
            create_manual_dataset_structure()
        elif choice == '2':
            show_existing_datasets()
        elif choice == '3':
            print("👋 ¡Hasta luego!")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
