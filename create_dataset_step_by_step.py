"""
Creador de dataset paso a paso - Un objeto a la vez
"""

import cv2
import os
import json
from datetime import datetime

def create_single_object_dataset():
    """Crear dataset para un solo tipo de objeto"""
    print("🎯 Creador de Dataset - Un Objeto a la Vez")
    print("=" * 50)
    
    print("\n📋 Objetos recomendados para entrenar:")
    print("1. 💊 Pastillas/Medicamentos")
    print("2. 📱 Teléfonos/Celulares") 
    print("3. 💻 Laptops/Computadoras")
    print("4. 🍼 Botellas específicas")
    print("5. 📄 Documentos/Papeles")
    print("6. 🔧 Herramientas")
    print("7. 🎮 Controles/Mandos")
    print("8. 🔑 Llaves")
    print("9. 👓 Lentes/Anteojos")
    print("10. ⌚ Relojes")
    
    # Seleccionar objeto
    object_name = input("\n🏷️ ¿Qué objeto quieres entrenar? (ej: pastillas): ").strip().lower()
    
    if not object_name:
        print("❌ Nombre de objeto requerido")
        return
    
    # Configurar dataset
    dataset_name = f"dataset_{object_name}"
    dataset_path = f"datasets/{dataset_name}"
    
    # Crear directorios
    dirs_to_create = [
        f"{dataset_path}/raw_images",
        f"{dataset_path}/images/train",
        f"{dataset_path}/images/val",
        f"{dataset_path}/labels/train", 
        f"{dataset_path}/labels/val"
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
    
    # Guardar configuración
    config = {
        "object_name": object_name,
        "dataset_name": dataset_name,
        "created_at": datetime.now().isoformat(),
        "classes": [object_name],
        "total_images": 0
    }
    
    with open(f"{dataset_path}/config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n✅ Dataset '{dataset_name}' creado")
    print(f"📁 Ubicación: {dataset_path}")
    
    return dataset_path, object_name

def capture_images_for_object(dataset_path, object_name):
    """Capturar imágenes para el objeto específico"""
    print(f"\n📸 Capturando imágenes para: {object_name}")
    print("=" * 50)
    
    print("🎯 CONSEJOS PARA BUENAS IMÁGENES:")
    print(f"- 📱 Muestra el {object_name} desde diferentes ángulos")
    print("- 💡 Usa diferentes tipos de iluminación")
    print("- 🎨 Cambia el fondo (mesa, mano, diferentes superficies)")
    print("- 📏 Diferentes distancias (cerca, lejos)")
    print("- 🔄 Diferentes orientaciones")
    print(f"- 🎯 Meta: 50-100 imágenes de {object_name}")
    
    print("\n📋 CONTROLES:")
    print("- ESPACIO: Capturar imagen")
    print("- Q: Terminar captura")
    print("- H: Mostrar ayuda")
    
    # Inicializar cámara
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Error: No se pudo abrir la cámara")
        return 0
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    images_captured = 0
    
    print(f"\n🎬 Iniciando captura para: {object_name}")
    print("💡 Coloca el objeto en diferentes posiciones y presiona ESPACIO")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Voltear para efecto espejo
            frame = cv2.flip(frame, 1)
            
            # Mostrar información en pantalla
            cv2.putText(frame, f"Objeto: {object_name}", (10, 30), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, f"Imagenes: {images_captured}", (10, 70), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, "ESPACIO: Capturar | Q: Terminar", 
                      (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Marco de enfoque
            cv2.rectangle(frame, (100, 100), (frame.shape[1]-100, frame.shape[0]-100), (0, 255, 0), 2)
            cv2.putText(frame, "Coloca objeto aqui", (110, 130), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Mostrar progreso
            progress_width = int((images_captured / 100) * 400)  # Meta: 100 imágenes
            cv2.rectangle(frame, (10, frame.shape[0] - 60), (410, frame.shape[0] - 40), (50, 50, 50), -1)
            cv2.rectangle(frame, (10, frame.shape[0] - 60), (10 + progress_width, frame.shape[0] - 40), (0, 255, 0), -1)
            cv2.putText(frame, f"Progreso: {images_captured}/100", (10, frame.shape[0] - 70), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            cv2.imshow(f'Capturando: {object_name}', frame)
            
            key = cv2.waitKey(1) & 0xFF
            
            # Capturar imagen
            if key == ord(' '):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"{object_name}_{images_captured:04d}_{timestamp}.jpg"
                filepath = os.path.join(dataset_path, "raw_images", filename)
                
                cv2.imwrite(filepath, frame)
                images_captured += 1
                
                print(f"📸 Capturada: {filename} (Total: {images_captured})")
                
                # Feedback visual
                cv2.putText(frame, "¡CAPTURADA!", (frame.shape[1]//2 - 100, frame.shape[0]//2), 
                          cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
                cv2.imshow(f'Capturando: {object_name}', frame)
                cv2.waitKey(200)  # Mostrar feedback por 200ms
            
            # Ayuda
            elif key == ord('h'):
                print(f"\n💡 CONSEJOS PARA {object_name.upper()}:")
                if "pastilla" in object_name or "medicamento" in object_name:
                    print("- 💊 Muestra pastillas individuales y en grupos")
                    print("- 📦 Incluye cajas de medicamentos")
                    print("- 🔍 Diferentes tamaños y colores")
                elif "telefono" in object_name or "celular" in object_name:
                    print("- 📱 Pantalla encendida y apagada")
                    print("- 🔄 Diferentes orientaciones (vertical/horizontal)")
                    print("- 🤳 En mano y sobre superficies")
                elif "laptop" in object_name or "computadora" in object_name:
                    print("- 💻 Abierta y cerrada")
                    print("- 🔌 Con y sin cables")
                    print("- 💡 Pantalla encendida y apagada")
                else:
                    print(f"- 🎯 Muestra {object_name} desde todos los ángulos")
                    print("- 💡 Diferentes condiciones de luz")
                    print("- 🎨 Diferentes fondos")
                print()
            
            # Salir
            elif key == ord('q'):
                break
                
    except KeyboardInterrupt:
        print("\n⏹️ Captura interrumpida")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
    
    # Actualizar configuración
    config_path = os.path.join(dataset_path, "config.json")
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    config["total_images"] = images_captured
    config["capture_completed_at"] = datetime.now().isoformat()
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n✅ Captura completada!")
    print(f"📊 Total imágenes: {images_captured}")
    print(f"📁 Guardadas en: {os.path.join(dataset_path, 'raw_images')}")
    
    return images_captured

def show_next_steps(dataset_path, object_name, images_captured):
    """Mostrar próximos pasos"""
    print(f"\n🎯 PRÓXIMOS PASOS PARA {object_name.upper()}:")
    print("=" * 50)
    
    if images_captured < 30:
        print("⚠️ RECOMENDACIÓN: Captura más imágenes")
        print(f"   📊 Tienes: {images_captured} imágenes")
        print("   🎯 Recomendado: 50-100 imágenes mínimo")
        print("   💡 Ejecuta de nuevo para capturar más")
    else:
        print("✅ ¡Buen trabajo! Tienes suficientes imágenes")
    
    print(f"\n📋 SIGUIENTE FASE - ANOTACIÓN:")
    print("1. 🌐 Ve a: https://roboflow.com/")
    print("2. 📤 Sube las imágenes de:")
    print(f"   📁 {os.path.join(dataset_path, 'raw_images')}")
    print(f"3. 🏷️ Anota cada {object_name} con bounding boxes")
    print("4. 📊 Divide en 70% train, 20% val, 10% test")
    print("5. 📥 Exporta en formato 'YOLOv8'")
    print("6. 📂 Descarga y coloca en:")
    print(f"   📁 {dataset_path}/")
    
    print(f"\n🤖 DESPUÉS DEL ANOTADO:")
    print("1. 🚀 Ejecuta: python train_custom_yolo.py")
    print(f"2. 🎯 Entrena modelo para {object_name}")
    print("3. 🔄 Usa modelo personalizado")
    print("4. 🎉 ¡Detección 95%+ precisa!")
    
    print(f"\n📈 RESULTADOS ESPERADOS:")
    print(f"- 🎯 {object_name}: 95%+ precisión")
    print("- ❌ Menos falsos positivos")
    print("- 🚀 Detección más rápida")
    print("- 🎨 Funciona en diferentes condiciones")

def main():
    """Función principal"""
    print("🚀 Creador de Dataset Personalizado - Paso a Paso")
    print("Vamos a crear un dataset para UN objeto específico")
    
    # Paso 1: Crear dataset
    result = create_single_object_dataset()
    if not result:
        return
    
    dataset_path, object_name = result
    
    # Paso 2: Capturar imágenes
    print(f"\n¿Quieres capturar imágenes para '{object_name}' ahora? (s/n): ", end="")
    if input().lower().startswith('s'):
        images_captured = capture_images_for_object(dataset_path, object_name)
        
        # Paso 3: Mostrar próximos pasos
        show_next_steps(dataset_path, object_name, images_captured)
    else:
        print(f"📁 Dataset creado en: {dataset_path}")
        print("💡 Ejecuta de nuevo cuando quieras capturar imágenes")

if __name__ == "__main__":
    main()
