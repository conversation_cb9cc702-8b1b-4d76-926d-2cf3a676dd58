"""
Servicio YOLO para detección de objetos
Detecta TODO tipo de objetos
"""

import cv2
import numpy as np
from ultralytics import YOL<PERSON>
from PIL import Image
import time
import json
from typing import List, Dict, Any, Tuple
import os

from ..core.config import settings

class YOLOService:
    """Servicio para detección de objetos con YOLO"""
    
    def __init__(self):
        self.model = None
        self.model_path = settings.YOLO_MODEL_PATH
        self.confidence = settings.YOLO_CONFIDENCE
        self.iou_threshold = settings.YOLO_IOU_THRESHOLD
        self.load_model()
    
    def load_model(self, model_path: str = None):
        """Cargar modelo YOLO"""
        try:
            if model_path:
                self.model_path = model_path
            
            print(f"🤖 Cargando modelo YOLO: {self.model_path}")
            self.model = YOLO(self.model_path)
            print("✅ Modelo YOLO cargado correctamente")
            
            # Mostrar clases disponibles
            if hasattr(self.model, 'names'):
                print(f"📋 Clases disponibles: {len(self.model.names)}")
                print(f"🏷️ Algunas clases: {list(self.model.names.values())[:10]}...")
            
        except Exception as e:
            print(f"❌ Error cargando modelo YOLO: {e}")
            self.model = None
    
    def detect_objects(self, image_path: str) -> Dict[str, Any]:
        """
        Detectar objetos en una imagen
        
        Args:
            image_path: Ruta de la imagen
            
        Returns:
            Dict con resultados de detección
        """
        if not self.model:
            raise Exception("Modelo YOLO no está cargado")
        
        start_time = time.time()
        
        try:
            # Cargar imagen
            image = cv2.imread(image_path)
            if image is None:
                raise Exception(f"No se pudo cargar la imagen: {image_path}")
            
            # Realizar detección
            results = self.model(
                image,
                conf=self.confidence,
                iou=self.iou_threshold,
                verbose=False
            )
            
            # Procesar resultados
            detections = self._process_results(results[0], image.shape)
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "image_path": image_path,
                "detections": detections,
                "total_objects": len(detections),
                "processing_time": processing_time,
                "model_used": self.model_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path,
                "processing_time": time.time() - start_time
            }
    
    def _process_results(self, result, image_shape: Tuple[int, int, int]) -> List[Dict[str, Any]]:
        """Procesar resultados de YOLO"""
        detections = []
        
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()  # Coordenadas
            confidences = result.boxes.conf.cpu().numpy()  # Confianzas
            classes = result.boxes.cls.cpu().numpy()  # Clases
            
            height, width = image_shape[:2]
            
            for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                x1, y1, x2, y2 = box
                
                # Convertir a coordenadas relativas (0-1)
                detection = {
                    "id": i,
                    "class_id": int(cls),
                    "class_name": self.model.names[int(cls)],
                    "confidence": float(conf),
                    "bbox": {
                        "x1": float(x1),
                        "y1": float(y1),
                        "x2": float(x2),
                        "y2": float(y2),
                        "width": float(x2 - x1),
                        "height": float(y2 - y1)
                    },
                    "bbox_normalized": {
                        "x1": float(x1 / width),
                        "y1": float(y1 / height),
                        "x2": float(x2 / width),
                        "y2": float(y2 / height)
                    }
                }
                detections.append(detection)
        
        return detections
    
    def detect_and_annotate(self, image_path: str, output_path: str = None) -> Dict[str, Any]:
        """
        Detectar objetos y crear imagen anotada
        
        Args:
            image_path: Ruta de imagen original
            output_path: Ruta para guardar imagen anotada
            
        Returns:
            Dict con resultados y ruta de imagen anotada
        """
        # Realizar detección
        results = self.detect_objects(image_path)
        
        if not results["success"]:
            return results
        
        try:
            # Cargar imagen original
            image = cv2.imread(image_path)
            annotated_image = image.copy()
            
            # Dibujar detecciones
            for detection in results["detections"]:
                bbox = detection["bbox"]
                x1, y1, x2, y2 = int(bbox["x1"]), int(bbox["y1"]), int(bbox["x2"]), int(bbox["y2"])
                
                # Dibujar rectángulo
                cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Dibujar etiqueta
                label = f"{detection['class_name']}: {detection['confidence']:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(annotated_image, (x1, y1 - label_size[1] - 10), 
                            (x1 + label_size[0], y1), (0, 255, 0), -1)
                cv2.putText(annotated_image, label, (x1, y1 - 5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
            
            # Guardar imagen anotada
            if not output_path:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = os.path.join(settings.TEMP_DIR, f"{base_name}_annotated.jpg")
            
            cv2.imwrite(output_path, annotated_image)
            
            results["annotated_image_path"] = output_path
            return results
            
        except Exception as e:
            results["annotation_error"] = str(e)
            return results
    
    def get_available_classes(self) -> List[str]:
        """Obtener lista de clases que puede detectar el modelo"""
        if not self.model or not hasattr(self.model, 'names'):
            return []
        return list(self.model.names.values())
    
    def search_objects(self, image_path: str, target_objects: List[str]) -> Dict[str, Any]:
        """
        Buscar objetos específicos en una imagen
        
        Args:
            image_path: Ruta de la imagen
            target_objects: Lista de objetos a buscar
            
        Returns:
            Dict con objetos encontrados
        """
        results = self.detect_objects(image_path)
        
        if not results["success"]:
            return results
        
        # Filtrar solo los objetos buscados
        found_objects = []
        for detection in results["detections"]:
            if detection["class_name"].lower() in [obj.lower() for obj in target_objects]:
                found_objects.append(detection)
        
        results["detections"] = found_objects
        results["total_objects"] = len(found_objects)
        results["search_targets"] = target_objects
        results["found_targets"] = list(set([det["class_name"] for det in found_objects]))
        
        return results
