"""
Rutas para detección de objetos y texto
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import shutil
import uuid
from datetime import datetime

from ...core.config import settings
from ...core.database import get_db, DetectionResult
from ...services.yolo_service import YOLOService
# from ...services.ocr_service import OCRService
from sqlalchemy.orm import Session
import json

router = APIRouter()

def save_uploaded_file(upload_file: UploadFile, directory: str) -> str:
    """Guardar archivo subido"""
    # Generar nombre único
    file_extension = os.path.splitext(upload_file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(directory, unique_filename)

    # Guardar archivo
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)

    return file_path

@router.post("/objects")
async def detect_objects(
    image: UploadFile = File(...),
    confidence: Optional[float] = None,
    annotate: bool = False,
    yolo_service: YOLOService = Depends(),
    db: Session = Depends(get_db)
):
    """
    Detectar objetos en una imagen usando YOLO

    Args:
        image: Archivo de imagen
        confidence: Umbral de confianza (opcional)
        annotate: Si crear imagen anotada

    Returns:
        Resultados de detección
    """
    # Validar archivo
    if not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    if image.size > settings.MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="Archivo demasiado grande")

    try:
        # Guardar imagen
        image_path = save_uploaded_file(image, settings.UPLOAD_DIR)

        # Ajustar confianza si se proporciona
        if confidence:
            original_confidence = yolo_service.confidence
            yolo_service.confidence = confidence

        # Detectar objetos
        if annotate:
            results = yolo_service.detect_and_annotate(image_path)
        else:
            results = yolo_service.detect_objects(image_path)

        # Restaurar confianza original
        if confidence:
            yolo_service.confidence = original_confidence

        # Guardar en base de datos
        db_result = DetectionResult(
            image_path=image_path,
            detection_type="yolo",
            objects_detected=json.dumps(results.get("detections", [])),
            confidence_scores=json.dumps([det.get("confidence", 0) for det in results.get("detections", [])]),
            processing_time=results.get("processing_time", 0)
        )
        db.add(db_result)
        db.commit()

        # Agregar ID de base de datos a la respuesta
        results["db_id"] = db_result.id

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

# @router.post("/text")
# async def detect_text(
#     image: UploadFile = File(...),
#     method: str = "easyocr",  # easyocr, tesseract, combined
#     annotate: bool = False,
#     ocr_service: OCRService = Depends(),
#     db: Session = Depends(get_db)
# ):
    """
    Extraer texto de una imagen usando OCR

    Args:
        image: Archivo de imagen
        method: Método OCR (easyocr, tesseract, combined)
        annotate: Si crear imagen anotada

    Returns:
        Texto extraído
    """
    # Validar archivo
    if not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen
        image_path = save_uploaded_file(image, settings.UPLOAD_DIR)

        # Extraer texto según método
        if method == "easyocr":
            if annotate:
                results = ocr_service.extract_and_annotate(image_path)
            else:
                results = ocr_service.extract_text_easyocr(image_path)
        elif method == "tesseract":
            results = ocr_service.extract_text_tesseract(image_path)
        elif method == "combined":
            results = ocr_service.extract_text_combined(image_path)
        else:
            raise HTTPException(status_code=400, detail="Método OCR no válido")

        # Detectar patrones específicos
        if results["success"] and results.get("full_text"):
            patterns = ocr_service.detect_specific_patterns(results["full_text"])
            results["detected_patterns"] = patterns

        # Guardar en base de datos
        db_result = DetectionResult(
            image_path=image_path,
            detection_type="ocr",
            objects_detected=json.dumps(results.get("text_detections", [])),
            confidence_scores=json.dumps([det.get("confidence", 0) for det in results.get("text_detections", [])]),
            processing_time=results.get("processing_time", 0)
        )
        db.add(db_result)
        db.commit()

        results["db_id"] = db_result.id

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@router.post("/combined")
async def detect_combined(
    image: UploadFile = File(...),
    detect_objects: bool = True,
    detect_text: bool = True,
    confidence: Optional[float] = None,
    yolo_service: YOLOService = Depends(),
    ocr_service: OCRService = Depends(),
    db: Session = Depends(get_db)
):
    """
    Detección combinada: objetos + texto

    Args:
        image: Archivo de imagen
        detect_objects: Si detectar objetos
        detect_text: Si detectar texto
        confidence: Umbral de confianza para YOLO

    Returns:
        Resultados combinados
    """
    # Validar archivo
    if not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen
        image_path = save_uploaded_file(image, settings.UPLOAD_DIR)

        results = {
            "success": True,
            "image_path": image_path,
            "detection_types": []
        }

        # Detectar objetos
        if detect_objects:
            if confidence:
                original_confidence = yolo_service.confidence
                yolo_service.confidence = confidence

            object_results = yolo_service.detect_objects(image_path)
            results["object_detection"] = object_results
            results["detection_types"].append("objects")

            if confidence:
                yolo_service.confidence = original_confidence

        # Detectar texto
        if detect_text:
            text_results = ocr_service.extract_text_easyocr(image_path)
            results["text_detection"] = text_results
            results["detection_types"].append("text")

            # Detectar patrones en texto
            if text_results["success"] and text_results.get("full_text"):
                patterns = ocr_service.detect_specific_patterns(text_results["full_text"])
                results["detected_patterns"] = patterns

        # Calcular tiempo total
        total_time = 0
        if "object_detection" in results:
            total_time += results["object_detection"].get("processing_time", 0)
        if "text_detection" in results:
            total_time += results["text_detection"].get("processing_time", 0)

        results["total_processing_time"] = total_time

        # Guardar en base de datos
        db_result = DetectionResult(
            image_path=image_path,
            detection_type="combined",
            objects_detected=json.dumps(results),
            confidence_scores=json.dumps([]),
            processing_time=total_time
        )
        db.add(db_result)
        db.commit()

        results["db_id"] = db_result.id

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@router.post("/search")
async def search_objects(
    image: UploadFile = File(...),
    targets: str = Form(...),  # Lista separada por comas
    yolo_service: YOLOService = Depends(),
    db: Session = Depends(get_db)
):
    """
    Buscar objetos específicos en una imagen

    Args:
        image: Archivo de imagen
        targets: Objetos a buscar (separados por comas)

    Returns:
        Objetos encontrados
    """
    # Validar archivo
    if not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen
        image_path = save_uploaded_file(image, settings.UPLOAD_DIR)

        # Procesar lista de objetivos
        target_list = [target.strip() for target in targets.split(",")]

        # Buscar objetos
        results = yolo_service.search_objects(image_path, target_list)

        # Guardar en base de datos
        db_result = DetectionResult(
            image_path=image_path,
            detection_type="search",
            objects_detected=json.dumps(results.get("detections", [])),
            confidence_scores=json.dumps([det.get("confidence", 0) for det in results.get("detections", [])]),
            processing_time=results.get("processing_time", 0)
        )
        db.add(db_result)
        db.commit()

        results["db_id"] = db_result.id

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error buscando objetos: {str(e)}")

@router.get("/classes")
async def get_available_classes(yolo_service: YOLOService = Depends()):
    """
    Obtener lista de clases que puede detectar YOLO

    Returns:
        Lista de clases disponibles
    """
    try:
        classes = yolo_service.get_available_classes()
        return {
            "success": True,
            "total_classes": len(classes),
            "classes": classes
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo clases: {str(e)}")

@router.get("/image/{filename}")
async def get_image(filename: str):
    """
    Obtener imagen procesada

    Args:
        filename: Nombre del archivo

    Returns:
        Archivo de imagen
    """
    file_path = os.path.join(settings.TEMP_DIR, filename)
    if not os.path.exists(file_path):
        # Buscar en directorio de uploads también
        file_path = os.path.join(settings.UPLOAD_DIR, filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Imagen no encontrada")

    return FileResponse(file_path)

@router.get("/history")
async def get_detection_history(
    limit: int = 10,
    detection_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Obtener historial de detecciones

    Args:
        limit: Número máximo de resultados
        detection_type: Filtrar por tipo de detección

    Returns:
        Historial de detecciones
    """
    try:
        query = db.query(DetectionResult)

        if detection_type:
            query = query.filter(DetectionResult.detection_type == detection_type)

        results = query.order_by(DetectionResult.created_at.desc()).limit(limit).all()

        history = []
        for result in results:
            history.append({
                "id": result.id,
                "image_path": result.image_path,
                "detection_type": result.detection_type,
                "processing_time": result.processing_time,
                "created_at": result.created_at.isoformat(),
                "objects_count": len(json.loads(result.objects_detected)) if result.objects_detected else 0
            })

        return {
            "success": True,
            "total_results": len(history),
            "history": history
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo historial: {str(e)}")
