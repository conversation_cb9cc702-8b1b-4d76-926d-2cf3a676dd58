"""
Backend principal para aplicación móvil con detección de voz y YOLO
Detecta TODO: objetos, texto, pastillas, etc.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from contextlib import asynccontextmanager

# Importar módulos locales
from app.core.config import settings
from app.core.database import create_tables
from app.api.routes import detection, voice, training
from app.services.yolo_service import YOLOService
from app.services.ocr_service import OCRService
from app.services.voice_service import VoiceService

# Variables globales para servicios
yolo_service = None
ocr_service = None
voice_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Inicializar servicios al arrancar la aplicación"""
    global yolo_service, ocr_service, voice_service

    print("🚀 Iniciando servicios...")

    # Crear tablas de base de datos
    create_tables()

    # Inicializar servicios
    yolo_service = YOLOService()
    ocr_service = OCRService()
    voice_service = VoiceService()

    print("✅ Servicios iniciados correctamente")
    yield

    print("🛑 Cerrando servicios...")

# Crear aplicación FastAPI
app = FastAPI(
    title="Mobile Voice Detection API",
    description="Backend para aplicación móvil con YOLO, OCR y comandos de voz",
    version="1.0.0",
    lifespan=lifespan
)

# Configurar CORS para móvil
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En producción, especificar dominios
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir rutas
app.include_router(detection.router, prefix="/api/v1/detection", tags=["detection"])
app.include_router(voice.router, prefix="/api/v1/voice", tags=["voice"])
app.include_router(training.router, prefix="/api/v1/training", tags=["training"])

@app.get("/")
async def root():
    """Endpoint de salud"""
    return {
        "message": "Mobile Voice Detection API",
        "status": "running",
        "version": "1.0.0",
        "services": {
            "yolo": yolo_service is not None,
            "ocr": ocr_service is not None,
            "voice": voice_service is not None
        }
    }

@app.get("/health")
async def health_check():
    """Verificar estado de todos los servicios"""
    return {
        "status": "healthy",
        "services": {
            "yolo": "ready" if yolo_service else "not_loaded",
            "ocr": "ready" if ocr_service else "not_loaded",
            "voice": "ready" if voice_service else "not_loaded",
            "database": "connected"
        }
    }

# Función para obtener servicios (dependency injection)
def get_yolo_service():
    if yolo_service is None:
        raise HTTPException(status_code=503, detail="YOLO service not available")
    return yolo_service

def get_ocr_service():
    if ocr_service is None:
        raise HTTPException(status_code=503, detail="OCR service not available")
    return ocr_service

def get_voice_service():
    if voice_service is None:
        raise HTTPException(status_code=503, detail="Voice service not available")
    return voice_service

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
