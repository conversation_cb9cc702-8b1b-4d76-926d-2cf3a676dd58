train: ../train/images
val: ../valid/images
test: ../test/images

nc: 41
names: ['- Auto-orientation of pixel data -with EXIF-orientation stripping-', '- Equal probability of one of the following 90-degree rotations- none- clockwise- counter-clockwise- upside-down', '- Random Gaussian blur of between 0 and 1.5 pixels', '- Random rotation of between -21 and -21 degrees', '- Resize to 600x600 -Stretch-', '- Salt and pepper noise was applied to 5 percent of pixels', '- annotate- and create datasets', '- collaborate with your team on computer vision projects', '- collect - organize images', '- export- train- and deploy computer vision models', '- understand and search unstructured image data', '- use active learning to improve your dataset over time', '------------------------------', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', 'Boxes are annotated in YOLO v5 PyTorch format.', 'For state of the art Computer Vision training notebooks you can use with this dataset-', 'MedicineBoxes - v1 2023-11-16 10-14am', 'Roboflow is an end-to-end computer vision platform that helps you', 'The dataset includes 2859 images.', 'The following augmentation was applied to create 3 versions of each source image-', 'The following pre-processing was applied to each image-', 'This dataset was exported via roboflow.com on November 22- 2023 at 5-32 PM GMT', 'To find over 100k other datasets and pre-trained models- visit https---universe.roboflow.com', 'visit https---github.com-roboflow-notebooks']

roboflow:
  workspace: stan-pd3de
  project: medicine-bxywx-12xp2
  version: 2
  license: CC BY 4.0
  url: https://universe.roboflow.com/stan-pd3de/medicine-bxywx-12xp2/dataset/2