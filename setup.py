"""
Script de configuración e instalación
"""

import os
import subprocess
import sys
import platform

def run_command(command, description):
    """Ejecutar comando y mostrar resultado"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completado")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error en {description}: {e}")
        print(f"Output: {e.output}")
        return False

def check_python_version():
    """Verificar versión de Python"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Se requiere Python 3.8 o superior")
        return False
    
    print("✅ Versión de Python compatible")
    return True

def install_system_dependencies():
    """Instalar dependencias del sistema"""
    system = platform.system().lower()
    
    if system == "windows":
        print("🪟 Sistema Windows detectado")
        print("⚠️ Asegúrate de tener Visual Studio Build Tools instalado")
        print("   Descarga: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
        
    elif system == "linux":
        print("🐧 Sistema Linux detectado")
        print("📦 Instalando dependencias del sistema...")
        
        # Dependencias para OpenCV, audio, etc.
        commands = [
            "sudo apt-get update",
            "sudo apt-get install -y python3-dev python3-pip",
            "sudo apt-get install -y libgl1-mesa-glx libglib2.0-0",
            "sudo apt-get install -y libsm6 libxext6 libxrender-dev",
            "sudo apt-get install -y libportaudio2 libportaudiocpp0 portaudio19-dev",
            "sudo apt-get install -y tesseract-ocr tesseract-ocr-spa",
            "sudo apt-get install -y ffmpeg"
        ]
        
        for cmd in commands:
            run_command(cmd, f"Ejecutando: {cmd}")
            
    elif system == "darwin":
        print("🍎 Sistema macOS detectado")
        print("📦 Instalando dependencias con Homebrew...")
        
        commands = [
            "brew install portaudio",
            "brew install tesseract",
            "brew install tesseract-lang",
            "brew install ffmpeg"
        ]
        
        for cmd in commands:
            run_command(cmd, f"Ejecutando: {cmd}")

def create_virtual_environment():
    """Crear entorno virtual"""
    if os.path.exists("venv"):
        print("📁 Entorno virtual ya existe")
        return True
    
    return run_command("python -m venv venv", "Creando entorno virtual")

def activate_and_install():
    """Activar entorno virtual e instalar dependencias"""
    system = platform.system().lower()
    
    if system == "windows":
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    print("📦 Instalando dependencias de Python...")
    
    # Actualizar pip
    run_command(f"{pip_cmd} install --upgrade pip", "Actualizando pip")
    
    # Instalar dependencias básicas primero
    basic_deps = [
        "wheel",
        "setuptools",
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",
        "pydantic",
        "pydantic-settings",
        "python-dotenv",
        "sqlalchemy",
        "requests"
    ]
    
    for dep in basic_deps:
        run_command(f"{pip_cmd} install {dep}", f"Instalando {dep}")
    
    # Instalar PyTorch (CPU version)
    print("🔥 Instalando PyTorch...")
    run_command(f"{pip_cmd} install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu", 
                "Instalando PyTorch CPU")
    
    # Instalar ultralytics (YOLO)
    run_command(f"{pip_cmd} install ultralytics", "Instalando YOLO (Ultralytics)")
    
    # Instalar OpenCV
    run_command(f"{pip_cmd} install opencv-python", "Instalando OpenCV")
    
    # Instalar dependencias de OCR
    run_command(f"{pip_cmd} install easyocr", "Instalando EasyOCR")
    run_command(f"{pip_cmd} install pytesseract", "Instalando PyTesseract")
    
    # Instalar dependencias de voz
    print("🎤 Instalando dependencias de voz...")
    run_command(f"{pip_cmd} install openai-whisper", "Instalando Whisper")
    run_command(f"{pip_cmd} install pyttsx3", "Instalando pyttsx3")
    run_command(f"{pip_cmd} install SpeechRecognition", "Instalando SpeechRecognition")
    
    # PyAudio puede ser problemático, intentar instalar
    print("🔊 Instalando PyAudio...")
    if not run_command(f"{pip_cmd} install pyaudio", "Instalando PyAudio"):
        print("⚠️ PyAudio falló. Intentando alternativa...")
        run_command(f"{pip_cmd} install pipwin", "Instalando pipwin")
        run_command(f"venv\\Scripts\\pipwin install pyaudio", "Instalando PyAudio con pipwin")
    
    # Instalar resto de dependencias
    other_deps = [
        "Pillow",
        "numpy",
        "scikit-image",
        "matplotlib",
        "aiofiles",
        "python-jose[cryptography]",
        "passlib[bcrypt]"
    ]
    
    for dep in other_deps:
        run_command(f"{pip_cmd} install {dep}", f"Instalando {dep}")

def download_initial_models():
    """Descargar modelos iniciales"""
    print("📥 Descargando modelos iniciales...")
    
    # Crear directorio de modelos
    os.makedirs("models", exist_ok=True)
    
    # El modelo YOLO se descargará automáticamente en el primer uso
    print("✅ Los modelos YOLO se descargarán automáticamente en el primer uso")
    
    # Whisper también se descarga automáticamente
    print("✅ Los modelos Whisper se descargarán automáticamente en el primer uso")

def create_example_files():
    """Crear archivos de ejemplo"""
    print("📝 Creando archivos de ejemplo...")
    
    # Crear .env si no existe
    if not os.path.exists(".env"):
        print("📄 Creando archivo .env...")
        # El archivo .env ya fue creado anteriormente
    
    # Crear directorio de ejemplos
    os.makedirs("examples", exist_ok=True)
    
    # Crear script de ejemplo
    example_script = """
# Ejemplo de uso de la API

import requests

# Probar salud de la API
response = requests.get("http://localhost:8000/health")
print("Estado de la API:", response.json())

# Ejemplo de detección de objetos
# with open("imagen.jpg", "rb") as f:
#     files = {"image": f}
#     response = requests.post("http://localhost:8000/api/v1/detection/objects", files=files)
#     print("Objetos detectados:", response.json())
"""
    
    with open("examples/example_usage.py", "w") as f:
        f.write(example_script)
    
    print("✅ Archivos de ejemplo creados en examples/")

def main():
    """Función principal de instalación"""
    print("🚀 Configurando Mobile Voice Detection API")
    print("=" * 50)
    
    # Verificar Python
    if not check_python_version():
        return
    
    # Instalar dependencias del sistema
    install_system_dependencies()
    
    # Crear entorno virtual
    if not create_virtual_environment():
        print("❌ Error creando entorno virtual")
        return
    
    # Instalar dependencias de Python
    activate_and_install()
    
    # Descargar modelos
    download_initial_models()
    
    # Crear archivos de ejemplo
    create_example_files()
    
    print("\n" + "=" * 50)
    print("✅ ¡Instalación completada!")
    print("\n📋 Próximos pasos:")
    print("1. Activar entorno virtual:")
    
    system = platform.system().lower()
    if system == "windows":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("2. Ejecutar servidor:")
    print("   python run.py")
    print("3. Abrir navegador:")
    print("   http://localhost:8000/docs")
    print("\n🎯 ¡Tu backend está listo para detectar TODO!")

if __name__ == "__main__":
    main()
