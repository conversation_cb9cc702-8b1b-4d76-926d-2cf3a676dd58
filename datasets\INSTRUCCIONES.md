# Datasets Creados - 2025-05-23 22:17

## 📊 Total: 32 datasets creados

## 📋 Lista de Datasets:
- manual_personas_con_armas/raw_images/ ← Agrega imágenes de personas_con_armas
- manual_armas_de_fuego/raw_images/ ← Agrega imágenes de armas_de_fuego
- manual_cuchillos/raw_images/ ← Agrega imágenes de cuchillos
- manual_tren/raw_images/ ← Agrega imágenes de tren
- manual_autos/raw_images/ ← Agrega imágenes de autos
- manual_motos/raw_images/ ← Agrega imágenes de motos
- manual_bicicletas/raw_images/ ← Agrega imágenes de bicicletas
- manual_camiones/raw_images/ ← Agrega imágenes de camiones
- manual_plantas/raw_images/ ← Agrega imágenes de plantas
- manual_arboles/raw_images/ ← Agrega imágenes de arboles
- manual_flores/raw_images/ ← Agrega imágenes de flores
- manual_personas/raw_images/ ← Agrega imágenes de personas
- manual_rostros/raw_images/ ← Agrega imágenes de rostros
- manual_manos/raw_images/ ← Agrega imágenes de manos
- manual_comida/raw_images/ ← Agrega imágenes de comida
- manual_bebidas/raw_images/ ← Agrega imágenes de bebidas
- manual_libros/raw_images/ ← Agrega imágenes de libros
- manual_herramientas/raw_images/ ← Agrega imágenes de herramientas
- manual_computadoras/raw_images/ ← Agrega imágenes de computadoras
- manual_televisores/raw_images/ ← Agrega imágenes de televisores
- manual_camaras/raw_images/ ← Agrega imágenes de camaras
- manual_muebles/raw_images/ ← Agrega imágenes de muebles
- manual_ropa/raw_images/ ← Agrega imágenes de ropa
- manual_medicamentos/raw_images/ ← Agrega imágenes de medicamentos
- manual_jeringas/raw_images/ ← Agrega imágenes de jeringas
- manual_documentos_identidad/raw_images/ ← Agrega imágenes de documentos_identidad
- manual_billetes/raw_images/ ← Agrega imágenes de billetes
- manual_tarjetas/raw_images/ ← Agrega imágenes de tarjetas
- manual_pelotas_deportivas/raw_images/ ← Agrega imágenes de pelotas_deportivas
- manual_fuego/raw_images/ ← Agrega imágenes de fuego
- manual_humo/raw_images/ ← Agrega imágenes de humo
- manual_accidentes/raw_images/ ← Agrega imágenes de accidentes


## 🎯 Instrucciones de Uso:

### Para cada dataset:
1. 📁 Ve a: datasets/manual_[objeto]/raw_images/
2. 📸 Agrega 50-100 imágenes del objeto
3. 🌐 Ve a: https://roboflow.com/
4. 📤 Sube las imágenes
5. 🏷️ Anota cada objeto con bounding boxes
6. 📊 Divide: 70% train, 20% val, 10% test
7. 📥 Exporta en formato 'YOLOv8'
8. 📂 Descarga y extrae en la carpeta del dataset
9. 🤖 Entrena: python train_custom_yolo.py

## 💡 Fuentes de Imágenes:
- 📱 Fotos con tu teléfono
- 🌐 Google Images
- 📹 Capturas de video
- 🎥 Frames de películas
- 📷 Unsplash, Pixabay

## 🎯 Consejos Específicos:

### Seguridad:
- 🔫 **Armas**: Diferentes tipos, en manos, sobre mesa
- 👤 **Personas con armas**: Diferentes poses, uniformes

### Transporte:
- 🚗 **Autos**: Diferentes marcas, colores, ángulos
- 🏍️ **Motos**: Deportivas, cruiser, scooters
- 🚂 **Trenes**: Pasajeros, carga, metro

### Naturaleza:
- 🌱 **Plantas**: Interior, exterior, diferentes especies
- 🌳 **Árboles**: Diferentes tipos, estaciones

### Personas:
- 👤 **Personas**: Diferentes edades, ropa, poses
- 😊 **Rostros**: Diferentes expresiones, ángulos
- ✋ **Manos**: Diferentes gestos, objetos

### Documentos:
- 🆔 **Documentos**: Cédulas, pasaportes, licencias
- 💵 **Billetes**: Diferentes denominaciones
- 💳 **Tarjetas**: Crédito, débito, identificación

### Emergencias:
- 🔥 **Fuego**: Incendios, fogatas, diferentes intensidades
- 💨 **Humo**: Diferentes densidades, colores
- 🚨 **Accidentes**: Vehículos, personas, diferentes tipos

## 🎉 Resultado Esperado:
- 🎯 Detección 95%+ precisa para cada objeto
- ❌ Menos falsos positivos
- 🚀 Modelos personalizados optimizados
