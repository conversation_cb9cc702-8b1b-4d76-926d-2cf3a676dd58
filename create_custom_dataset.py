"""
Herramienta para crear dataset personalizado para YOLO
Captura imágenes y permite anotarlas para entrenamiento
"""

import cv2
import os
import json
import time
from datetime import datetime
import tkinter as tk
from tkinter import messagebox, simpledialog

class DatasetCreator:
    def __init__(self):
        self.dataset_name = None
        self.classes = []
        self.current_class = None
        self.images_captured = 0
        self.dataset_path = None
        
    def setup_dataset(self):
        """Configurar nuevo dataset"""
        print("🎯 Configurando nuevo dataset personalizado...")
        
        # Crear ventana para configuración
        root = tk.Tk()
        root.withdraw()  # Ocultar ventana principal
        
        # Nombre del dataset
        self.dataset_name = simpledialog.askstring(
            "Dataset", 
            "Nombre del dataset (ej: 'mis_objetos', 'pastillas', 'herramientas'):"
        )
        
        if not self.dataset_name:
            print("❌ Cancelado")
            return False
        
        # Clases a detectar
        classes_input = simpledialog.askstring(
            "Clases", 
            "Clases a detectar separadas por comas\n(ej: 'persona,telefono,laptop,botella'):"
        )
        
        if not classes_input:
            print("❌ Cancelado")
            return False
        
        self.classes = [cls.strip() for cls in classes_input.split(',')]
        
        root.destroy()
        
        # Crear estructura de directorios
        self.dataset_path = f"datasets/{self.dataset_name}"
        dirs_to_create = [
            f"{self.dataset_path}/images/train",
            f"{self.dataset_path}/images/val", 
            f"{self.dataset_path}/images/test",
            f"{self.dataset_path}/labels/train",
            f"{self.dataset_path}/labels/val",
            f"{self.dataset_path}/labels/test",
            f"{self.dataset_path}/raw_captures"
        ]
        
        for dir_path in dirs_to_create:
            os.makedirs(dir_path, exist_ok=True)
        
        # Crear archivo de configuración
        config = {
            "dataset_name": self.dataset_name,
            "classes": self.classes,
            "created_at": datetime.now().isoformat(),
            "total_classes": len(self.classes)
        }
        
        with open(f"{self.dataset_path}/config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        # Crear dataset.yaml para YOLO
        yaml_content = f"""# Dataset personalizado: {self.dataset_name}
path: {os.path.abspath(self.dataset_path)}
train: images/train
val: images/val
test: images/test

# Clases
nc: {len(self.classes)}
names: {self.classes}
"""
        
        with open(f"{self.dataset_path}/dataset.yaml", 'w') as f:
            f.write(yaml_content)
        
        print(f"✅ Dataset '{self.dataset_name}' configurado")
        print(f"📁 Ubicación: {self.dataset_path}")
        print(f"🏷️ Clases: {', '.join(self.classes)}")
        
        return True
    
    def capture_images(self):
        """Capturar imágenes para el dataset"""
        if not self.dataset_path:
            print("❌ Primero configura el dataset")
            return
        
        print("\n📸 Modo captura de imágenes")
        print("🎯 Instrucciones:")
        print("   - Presiona ESPACIO para capturar imagen")
        print("   - Presiona 1-9 para cambiar de clase")
        print("   - Presiona 'q' para salir")
        print("   - Presiona 'h' para ayuda")
        
        # Inicializar cámara
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Error: No se pudo abrir la cámara")
            return
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        self.current_class = self.classes[0] if self.classes else "unknown"
        class_index = 0
        
        print(f"\n🏷️ Clase actual: {self.current_class}")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Voltear para efecto espejo
                frame = cv2.flip(frame, 1)
                
                # Mostrar información en pantalla
                cv2.putText(frame, f"Dataset: {self.dataset_name}", (10, 30), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, f"Clase: {self.current_class} ({class_index + 1}/{len(self.classes)})", 
                          (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, f"Imagenes: {self.images_captured}", (10, 90), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(frame, "ESPACIO: Capturar | 1-9: Cambiar clase | Q: Salir", 
                          (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # Dibujar marco de captura
                cv2.rectangle(frame, (50, 50), (frame.shape[1]-50, frame.shape[0]-50), (0, 255, 0), 2)
                
                cv2.imshow('Captura de Dataset - YOLO Personalizado', frame)
                
                key = cv2.waitKey(1) & 0xFF
                
                # Capturar imagen
                if key == ord(' '):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                    filename = f"{self.current_class}_{timestamp}.jpg"
                    filepath = os.path.join(self.dataset_path, "raw_captures", filename)
                    
                    cv2.imwrite(filepath, frame)
                    self.images_captured += 1
                    
                    print(f"📸 Capturada: {filename} (Total: {self.images_captured})")
                
                # Cambiar clase
                elif key >= ord('1') and key <= ord('9'):
                    new_index = key - ord('1')
                    if new_index < len(self.classes):
                        class_index = new_index
                        self.current_class = self.classes[class_index]
                        print(f"🏷️ Cambiado a clase: {self.current_class}")
                
                # Ayuda
                elif key == ord('h'):
                    print("\n📋 Clases disponibles:")
                    for i, cls in enumerate(self.classes):
                        print(f"   {i+1}: {cls}")
                    print()
                
                # Salir
                elif key == ord('q'):
                    break
                    
        except KeyboardInterrupt:
            print("\n⏹️ Captura interrumpida")
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
            print(f"\n✅ Captura finalizada. Total imágenes: {self.images_captured}")
            
            if self.images_captured > 0:
                print("\n📋 Próximos pasos:")
                print("1. Anotar las imágenes capturadas")
                print("2. Dividir en train/val/test")
                print("3. Entrenar el modelo personalizado")
    
    def show_annotation_instructions(self):
        """Mostrar instrucciones para anotar"""
        print("\n🏷️ INSTRUCCIONES PARA ANOTAR IMÁGENES:")
        print("=" * 50)
        print("Para entrenar YOLO necesitas anotar las imágenes capturadas.")
        print("\n📋 Opciones de anotación:")
        print("\n1. 🖥️ CVAT (Computer Vision Annotation Tool)")
        print("   - Herramienta profesional online")
        print("   - Visita: https://www.cvat.ai/")
        print("   - Sube tus imágenes y anota objetos")
        print("   - Exporta en formato YOLO")
        
        print("\n2. 🖱️ LabelImg (Desktop)")
        print("   - Instalar: pip install labelImg")
        print("   - Ejecutar: labelImg")
        print("   - Anotar imágenes localmente")
        
        print("\n3. 🌐 Roboflow (Recomendado)")
        print("   - Visita: https://roboflow.com/")
        print("   - Sube dataset completo")
        print("   - Anota online con IA asistida")
        print("   - Exporta directamente para YOLO")
        print("   - Incluye data augmentation automático")
        
        print("\n4. 📱 Makesense.ai")
        print("   - Herramienta web gratuita")
        print("   - Visita: https://www.makesense.ai/")
        print("   - Anota directamente en el navegador")
        
        print("\n🎯 RECOMENDACIÓN:")
        print("Usa Roboflow para mejores resultados:")
        print("- IA asistida para anotación más rápida")
        print("- Data augmentation automático")
        print("- Exportación directa a YOLO")
        print("- Métricas de calidad del dataset")

def main():
    print("🎯 Creador de Dataset Personalizado para YOLO")
    print("=" * 50)
    
    creator = DatasetCreator()
    
    while True:
        print("\n📋 Opciones:")
        print("1. 🆕 Configurar nuevo dataset")
        print("2. 📸 Capturar imágenes")
        print("3. 📖 Instrucciones de anotación")
        print("4. ❌ Salir")
        
        choice = input("\nElige una opción (1-4): ").strip()
        
        if choice == '1':
            if creator.setup_dataset():
                print(f"\n✅ Dataset '{creator.dataset_name}' listo para captura")
        
        elif choice == '2':
            if creator.dataset_path:
                creator.capture_images()
            else:
                print("❌ Primero configura un dataset (opción 1)")
        
        elif choice == '3':
            creator.show_annotation_instructions()
        
        elif choice == '4':
            print("👋 ¡Hasta luego!")
            break
        
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
