"""
Configuración de la aplicación
"""

import os
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """Configuración de la aplicación"""
    
    # Configuración del servidor
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Base de datos SQLite
    DATABASE_URL: str = "sqlite:///./mobile_detection.db"
    
    # Directorios
    UPLOAD_DIR: str = "./uploads"
    MODELS_DIR: str = "./models"
    DATASETS_DIR: str = "./datasets"
    TEMP_DIR: str = "./temp"
    
    # Configuración YOLO
    YOLO_MODEL_PATH: str = "yolov8n.pt"  # Empezamos con nano
    YOLO_CONFIDENCE: float = 0.25
    YOLO_IOU_THRESHOLD: float = 0.45
    
    # Configuración OCR
    OCR_LANGUAGES: list = ["es", "en"]  # Español e inglés
    OCR_GPU: bool = False  # Cambiar a True si tienes GPU
    
    # Configuración de voz
    VOICE_LANGUAGE: str = "es-ES"
    VOICE_RATE: int = 150  # Velocidad de habla
    VOICE_VOLUME: float = 0.9
    
    # Configuración de archivos
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_IMAGE_EXTENSIONS: set = {".jpg", ".jpeg", ".png", ".bmp", ".tiff"}
    ALLOWED_AUDIO_EXTENSIONS: set = {".wav", ".mp3", ".m4a", ".flac"}
    
    # Configuración de entrenamiento
    TRAINING_EPOCHS: int = 100
    TRAINING_BATCH_SIZE: int = 16
    TRAINING_IMAGE_SIZE: int = 640
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Crear instancia de configuración
settings = Settings()

# Crear directorios necesarios
def create_directories():
    """Crear directorios necesarios si no existen"""
    directories = [
        settings.UPLOAD_DIR,
        settings.MODELS_DIR,
        settings.DATASETS_DIR,
        settings.TEMP_DIR,
        f"{settings.DATASETS_DIR}/images/train",
        f"{settings.DATASETS_DIR}/images/val",
        f"{settings.DATASETS_DIR}/images/test",
        f"{settings.DATASETS_DIR}/labels/train",
        f"{settings.DATASETS_DIR}/labels/val",
        f"{settings.DATASETS_DIR}/labels/test",
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Directorio creado/verificado: {directory}")

# Crear directorios al importar
create_directories()
