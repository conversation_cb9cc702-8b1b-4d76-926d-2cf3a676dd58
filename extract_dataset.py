"""
Extractor y organizador de datasets desde archivos RAR/ZIP
"""

import os
import shutil
import zipfile
import rarfile
from pathlib import Path
import json
from datetime import datetime

def extract_rar_dataset(rar_path, dataset_name="pastillas"):
    """Extraer dataset desde archivo RAR"""
    print(f"📦 Extrayendo dataset de pastillas desde: {rar_path}")
    
    # Verificar que el archivo existe
    if not os.path.exists(rar_path):
        print(f"❌ Archivo no encontrado: {rar_path}")
        return False
    
    # Determinar el dataset de destino
    if dataset_name == "pastillas":
        # Usar el dataset existente de pastillas
        if os.path.exists("datasets/dataset_pastillas"):
            target_dir = "datasets/dataset_pastillas/raw_images"
        elif os.path.exists("datasets/manual_medicamentos"):
            target_dir = "datasets/manual_medicamentos/raw_images"
        else:
            # Crear nuevo dataset
            target_dir = "datasets/manual_pastillas/raw_images"
            os.makedirs(target_dir, exist_ok=True)
    else:
        target_dir = f"datasets/manual_{dataset_name}/raw_images"
        os.makedirs(target_dir, exist_ok=True)
    
    print(f"📁 Destino: {target_dir}")
    
    try:
        # Intentar extraer como RAR
        if rar_path.lower().endswith('.rar'):
            print("🔓 Extrayendo archivo RAR...")
            with rarfile.RarFile(rar_path) as rf:
                # Listar contenido
                file_list = rf.namelist()
                image_files = [f for f in file_list if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff'))]
                
                print(f"📊 Encontradas {len(image_files)} imágenes en el RAR")
                
                # Extraer solo imágenes
                extracted_count = 0
                for img_file in image_files:
                    try:
                        # Extraer archivo
                        rf.extract(img_file, "temp_extract")
                        
                        # Obtener nombre del archivo
                        filename = os.path.basename(img_file)
                        
                        # Renombrar si es necesario
                        if not filename.lower().startswith('pastilla'):
                            new_filename = f"pastilla_{extracted_count:04d}_{filename}"
                        else:
                            new_filename = filename
                        
                        # Mover a destino final
                        src_path = os.path.join("temp_extract", img_file)
                        dst_path = os.path.join(target_dir, new_filename)
                        
                        shutil.move(src_path, dst_path)
                        extracted_count += 1
                        
                        print(f"✅ Extraída: {new_filename}")
                        
                    except Exception as e:
                        print(f"⚠️ Error extrayendo {img_file}: {e}")
                
                # Limpiar directorio temporal
                if os.path.exists("temp_extract"):
                    shutil.rmtree("temp_extract")
                
                print(f"🎉 Extracción completada: {extracted_count} imágenes")
                return extracted_count
                
        # Intentar extraer como ZIP
        elif rar_path.lower().endswith('.zip'):
            print("🔓 Extrayendo archivo ZIP...")
            with zipfile.ZipFile(rar_path, 'r') as zf:
                # Listar contenido
                file_list = zf.namelist()
                image_files = [f for f in file_list if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff'))]
                
                print(f"📊 Encontradas {len(image_files)} imágenes en el ZIP")
                
                # Extraer solo imágenes
                extracted_count = 0
                for img_file in image_files:
                    try:
                        # Extraer archivo
                        zf.extract(img_file, "temp_extract")
                        
                        # Obtener nombre del archivo
                        filename = os.path.basename(img_file)
                        
                        # Renombrar si es necesario
                        if not filename.lower().startswith('pastilla'):
                            new_filename = f"pastilla_{extracted_count:04d}_{filename}"
                        else:
                            new_filename = filename
                        
                        # Mover a destino final
                        src_path = os.path.join("temp_extract", img_file)
                        dst_path = os.path.join(target_dir, new_filename)
                        
                        shutil.move(src_path, dst_path)
                        extracted_count += 1
                        
                        print(f"✅ Extraída: {new_filename}")
                        
                    except Exception as e:
                        print(f"⚠️ Error extrayendo {img_file}: {e}")
                
                # Limpiar directorio temporal
                if os.path.exists("temp_extract"):
                    shutil.rmtree("temp_extract")
                
                print(f"🎉 Extracción completada: {extracted_count} imágenes")
                return extracted_count
        
        else:
            print("❌ Formato no soportado. Use .rar o .zip")
            return False
            
    except Exception as e:
        print(f"❌ Error durante extracción: {e}")
        return False

def count_images_in_dataset(dataset_path):
    """Contar imágenes en un dataset"""
    if not os.path.exists(dataset_path):
        return 0
    
    image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
    count = 0
    
    for file in os.listdir(dataset_path):
        if file.lower().endswith(image_extensions):
            count += 1
    
    return count

def show_dataset_status():
    """Mostrar estado de datasets existentes"""
    print("\n📊 Estado de Datasets Existentes:")
    print("=" * 50)
    
    datasets_to_check = [
        ("dataset_pastillas", "Pastillas (capturadas)"),
        ("manual_medicamentos", "Medicamentos"),
        ("manual_pastillas", "Pastillas (manual)"),
        ("dataset_telefono", "Teléfonos"),
        ("manual_autos", "Autos"),
        ("manual_plantas", "Plantas")
    ]
    
    for dataset_dir, description in datasets_to_check:
        full_path = f"datasets/{dataset_dir}"
        if os.path.exists(full_path):
            raw_images_path = os.path.join(full_path, "raw_images")
            if os.path.exists(raw_images_path):
                count = count_images_in_dataset(raw_images_path)
                status = "✅ Listo para anotar" if count >= 30 else "⚠️ Necesita más imágenes"
                print(f"📁 {description:<20} → {count:>3} imágenes {status}")
            else:
                print(f"📁 {description:<20} → Sin carpeta raw_images")
        else:
            print(f"📁 {description:<20} → No existe")

def main():
    """Función principal"""
    print("📦 Extractor de Dataset desde RAR/ZIP")
    print("=" * 50)
    
    # Mostrar estado actual
    show_dataset_status()
    
    print("\n🎯 Opciones:")
    print("1. 📦 Extraer archivo RAR/ZIP")
    print("2. 📊 Ver estado de datasets")
    print("3. ❌ Salir")
    
    choice = input("\nElige opción (1-3): ").strip()
    
    if choice == '1':
        print("\n📁 Coloca tu archivo RAR/ZIP en la carpeta del proyecto")
        print("💡 Formatos soportados: .rar, .zip")
        
        # Buscar archivos RAR/ZIP en el directorio actual
        current_files = os.listdir('.')
        rar_zip_files = [f for f in current_files if f.lower().endswith(('.rar', '.zip'))]
        
        if rar_zip_files:
            print(f"\n📋 Archivos encontrados:")
            for i, file in enumerate(rar_zip_files):
                print(f"{i+1}. {file}")
            
            try:
                file_choice = int(input(f"\nSelecciona archivo (1-{len(rar_zip_files)}): ")) - 1
                if 0 <= file_choice < len(rar_zip_files):
                    selected_file = rar_zip_files[file_choice]
                    
                    # Preguntar nombre del dataset
                    dataset_name = input("Nombre del dataset (default: pastillas): ").strip() or "pastillas"
                    
                    # Extraer
                    result = extract_rar_dataset(selected_file, dataset_name)
                    
                    if result:
                        print(f"\n🎉 ¡Dataset extraído exitosamente!")
                        print(f"📊 {result} imágenes agregadas")
                        print(f"\n📋 Próximos pasos:")
                        print("1. 🌐 Ve a: https://roboflow.com/")
                        print("2. 📤 Sube las imágenes")
                        print("3. 🏷️ Anota cada pastilla con bounding boxes")
                        print("4. 📥 Exporta en formato YOLOv8")
                        print("5. 🤖 Entrena: python train_custom_yolo.py")
                    
                else:
                    print("❌ Selección inválida")
            except ValueError:
                print("❌ Entrada inválida")
        else:
            # Pedir ruta manual
            file_path = input("\nRuta del archivo RAR/ZIP: ").strip()
            if file_path:
                dataset_name = input("Nombre del dataset (default: pastillas): ").strip() or "pastillas"
                extract_rar_dataset(file_path, dataset_name)
            else:
                print("❌ No se encontraron archivos RAR/ZIP")
                print("💡 Coloca tu archivo en la carpeta del proyecto")
    
    elif choice == '2':
        show_dataset_status()
    
    elif choice == '3':
        print("👋 ¡Hasta luego!")
    
    else:
        print("❌ Opción inválida")

if __name__ == "__main__":
    main()
