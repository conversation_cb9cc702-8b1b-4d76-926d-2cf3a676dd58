"""
Servidor simple con YOLO para pruebas
"""

from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import shutil
import uuid
from ultralytics import YOLO
import cv2
import json

# Crear aplicación
app = FastAPI(
    title="YOLO Detection API",
    description="API simple para detección de objetos con YOLO",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Cargar modelo YOLO
print("🤖 Cargando modelo YOLO...")
model = YOLO('yolov8n.pt')
print("✅ Modelo YOLO cargado")

# Crear directorios
os.makedirs("uploads", exist_ok=True)
os.makedirs("temp", exist_ok=True)

@app.get("/")
async def root():
    """Endpoint principal"""
    return {
        "message": "YOLO Detection API",
        "status": "running",
        "model": "YOLOv8n",
        "classes": len(model.names) if hasattr(model, 'names') else 0
    }

@app.get("/classes")
async def get_classes():
    """Obtener clases disponibles"""
    if hasattr(model, 'names'):
        return {
            "success": True,
            "total_classes": len(model.names),
            "classes": list(model.names.values())
        }
    return {"success": False, "error": "No classes available"}

@app.post("/detect")
async def detect_objects(image: UploadFile = File(...)):
    """
    Detectar objetos en una imagen
    """
    # Validar archivo
    if not image.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen
        file_extension = os.path.splitext(image.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        image_path = os.path.join("uploads", unique_filename)

        with open(image_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # Detectar objetos
        print(f"🔍 Detectando objetos en: {image_path}")
        results = model(image_path, conf=0.25)

        # Procesar resultados
        detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy()

            for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                x1, y1, x2, y2 = box
                detection = {
                    "id": i,
                    "class_id": int(cls),
                    "class_name": model.names[int(cls)],
                    "confidence": float(conf),
                    "bbox": {
                        "x1": float(x1),
                        "y1": float(y1),
                        "x2": float(x2),
                        "y2": float(y2),
                        "width": float(x2 - x1),
                        "height": float(y2 - y1)
                    }
                }
                detections.append(detection)

        print(f"✅ Detectados {len(detections)} objetos")

        return {
            "success": True,
            "image_path": image_path,
            "total_objects": len(detections),
            "detections": detections,
            "model": "YOLOv8n"
        }

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@app.post("/detect-annotated")
async def detect_and_annotate(image: UploadFile = File(...)):
    """
    Detectar objetos y crear imagen anotada
    """
    # Validar archivo
    if not image.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen original
        file_extension = os.path.splitext(image.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        image_path = os.path.join("uploads", unique_filename)

        with open(image_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # Detectar objetos
        print(f"🔍 Detectando y anotando: {image_path}")
        results = model(image_path, conf=0.25)

        # Cargar imagen para anotar
        img = cv2.imread(image_path)
        annotated_img = img.copy()

        # Procesar y anotar resultados
        detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy()

            for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                x1, y1, x2, y2 = map(int, box)
                class_name = model.names[int(cls)]

                # Dibujar rectángulo
                cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # Dibujar etiqueta
                label = f"{class_name}: {conf:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(annotated_img, (x1, y1 - label_size[1] - 10),
                            (x1 + label_size[0], y1), (0, 255, 0), -1)
                cv2.putText(annotated_img, label, (x1, y1 - 5),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

                detection = {
                    "id": i,
                    "class_id": int(cls),
                    "class_name": class_name,
                    "confidence": float(conf),
                    "bbox": {
                        "x1": float(x1),
                        "y1": float(y1),
                        "x2": float(x2),
                        "y2": float(y2)
                    }
                }
                detections.append(detection)

        # Guardar imagen anotada
        annotated_filename = f"annotated_{unique_filename}"
        annotated_path = os.path.join("temp", annotated_filename)
        cv2.imwrite(annotated_path, annotated_img)

        print(f"✅ Detectados {len(detections)} objetos, imagen anotada guardada")

        return {
            "success": True,
            "original_image": image_path,
            "annotated_image": annotated_path,
            "total_objects": len(detections),
            "detections": detections,
            "model": "YOLOv8n"
        }

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@app.post("/search")
async def search_objects(image: UploadFile = File(...), targets: str = "person"):
    """
    Buscar objetos específicos
    """
    # Validar archivo
    if not image.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen
        file_extension = os.path.splitext(image.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        image_path = os.path.join("uploads", unique_filename)

        with open(image_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)

        # Procesar targets
        target_list = [t.strip().lower() for t in targets.split(",")]

        # Detectar objetos
        print(f"🔍 Buscando {target_list} en: {image_path}")
        results = model(image_path, conf=0.25)

        # Filtrar solo objetos buscados
        found_objects = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy()

            for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                class_name = model.names[int(cls)]

                if class_name.lower() in target_list:
                    x1, y1, x2, y2 = box
                    detection = {
                        "id": i,
                        "class_id": int(cls),
                        "class_name": class_name,
                        "confidence": float(conf),
                        "bbox": {
                            "x1": float(x1),
                            "y1": float(y1),
                            "x2": float(x2),
                            "y2": float(y2)
                        }
                    }
                    found_objects.append(detection)

        print(f"✅ Encontrados {len(found_objects)} objetos de {target_list}")

        return {
            "success": True,
            "image_path": image_path,
            "search_targets": target_list,
            "found_objects": found_objects,
            "total_found": len(found_objects)
        }

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=f"Error buscando objetos: {str(e)}")

if __name__ == "__main__":
    print("🚀 Iniciando servidor YOLO...")
    print("📍 Servidor: http://localhost:8001")
    print("📖 Documentación: http://localhost:8001/docs")
    print("-" * 50)

    uvicorn.run(app, host="0.0.0.0", port=8001)
