"""
Probar YOLO con las imágenes de medicamentos extraídas
"""

import cv2
import os
from ultralytics import YOLO
import random

def test_yolo_on_medicamentos():
    """Probar YOLO en imágenes de medicamentos"""
    print("🧪 Probando YOLO en Medicamentos Extraídos")
    print("=" * 50)
    
    # Cargar modelo YOLO
    print("🤖 Cargando modelo YOLO...")
    model = YOLO('yolov8n.pt')
    
    # Directorio de imágenes
    images_dir = "datasets/manual_medicamentos/raw_images"
    
    if not os.path.exists(images_dir):
        print(f"❌ No se encuentra el directorio: {images_dir}")
        return
    
    # Obtener lista de imágenes
    image_files = [f for f in os.listdir(images_dir) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not image_files:
        print("❌ No se encontraron imágenes")
        return
    
    print(f"📊 Encontradas {len(image_files)} imágenes de medicamentos")
    
    # Seleccionar algunas imágenes al azar para probar
    test_images = random.sample(image_files, min(5, len(image_files)))
    
    print(f"🎯 Probando con {len(test_images)} imágenes:")
    
    for i, img_file in enumerate(test_images):
        print(f"\n📸 Imagen {i+1}: {img_file}")
        
        # Cargar imagen
        img_path = os.path.join(images_dir, img_file)
        image = cv2.imread(img_path)
        
        if image is None:
            print(f"❌ Error cargando imagen: {img_file}")
            continue
        
        # Redimensionar si es muy grande
        height, width = image.shape[:2]
        if width > 800:
            scale = 800 / width
            new_width = 800
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))
        
        print(f"   📐 Tamaño: {image.shape[1]}x{image.shape[0]}")
        
        # Ejecutar detección
        print("   🔍 Ejecutando detección YOLO...")
        results = model(image)
        
        # Procesar resultados
        detections_found = False
        
        for result in results:
            boxes = result.boxes
            if boxes is not None and len(boxes) > 0:
                detections_found = True
                print(f"   ✅ {len(boxes)} objetos detectados:")
                
                for box in boxes:
                    # Obtener información de la detección
                    conf = float(box.conf[0])
                    cls = int(box.cls[0])
                    class_name = model.names[cls]
                    
                    print(f"      🎯 {class_name}: {conf:.2f} confianza")
                    
                    # Dibujar bounding box
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # Agregar etiqueta
                    label = f"{class_name}: {conf:.2f}"
                    cv2.putText(image, label, (x1, y1-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        if not detections_found:
            print("   ❌ No se detectaron objetos")
            # Agregar texto en la imagen
            cv2.putText(image, "No se detectaron objetos", (10, 30), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Mostrar imagen
        window_name = f"YOLO Test - {img_file[:30]}"
        cv2.imshow(window_name, image)
        
        print("   💡 Presiona cualquier tecla para continuar...")
        cv2.waitKey(0)
        cv2.destroyWindow(window_name)
    
    print("\n📊 RESUMEN DEL EXPERIMENTO:")
    print("=" * 50)
    print("🎯 YOLO estándar probablemente detectó:")
    print("   ✅ Personas (si aparecen en las imágenes)")
    print("   ✅ Botellas (si hay frascos de medicamentos)")
    print("   ❌ NO detectó pastillas específicas")
    print("   ❌ NO identificó tipos de medicamentos")
    
    print("\n💡 CONCLUSIÓN:")
    print("🔬 YOLO estándar NO está entrenado para medicamentos")
    print("💊 Necesitas entrenar un modelo personalizado")
    print("🎯 Con tu dataset de 100+ imágenes puedes lograr:")
    print("   - 95%+ precisión en detección de pastillas")
    print("   - Identificación de tipos específicos")
    print("   - Detección de sobredosis")
    print("   - Conteo automático de medicamentos")
    
    print("\n🚀 PRÓXIMO PASO:")
    print("1. 🌐 Anota las imágenes en Roboflow.com")
    print("2. 🤖 Entrena modelo personalizado")
    print("3. 🎉 ¡Detección 95%+ precisa de medicamentos!")

def test_single_image():
    """Probar con una sola imagen específica"""
    print("🧪 Prueba Rápida con Una Imagen")
    print("=" * 40)
    
    # Cargar modelo
    model = YOLO('yolov8n.pt')
    
    # Directorio de imágenes
    images_dir = "datasets/manual_medicamentos/raw_images"
    
    # Obtener primera imagen disponible
    image_files = [f for f in os.listdir(images_dir) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not image_files:
        print("❌ No hay imágenes disponibles")
        return
    
    # Usar la primera imagen
    test_image = image_files[0]
    img_path = os.path.join(images_dir, test_image)
    
    print(f"📸 Probando con: {test_image}")
    
    # Cargar y procesar imagen
    image = cv2.imread(img_path)
    
    # Redimensionar
    height, width = image.shape[:2]
    if width > 600:
        scale = 600 / width
        new_width = 600
        new_height = int(height * scale)
        image = cv2.resize(image, (new_width, new_height))
    
    # Ejecutar detección
    print("🔍 Ejecutando YOLO...")
    results = model(image)
    
    # Mostrar resultados
    detections = 0
    for result in results:
        boxes = result.boxes
        if boxes is not None:
            detections = len(boxes)
            print(f"✅ {detections} objetos detectados")
            
            for box in boxes:
                conf = float(box.conf[0])
                cls = int(box.cls[0])
                class_name = model.names[cls]
                print(f"   🎯 {class_name}: {conf:.2f}")
                
                # Dibujar en imagen
                x1, y1, x2, y2 = map(int, box.xyxy[0])
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                label = f"{class_name}: {conf:.2f}"
                cv2.putText(image, label, (x1, y1-10), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    if detections == 0:
        print("❌ No se detectaron objetos")
        cv2.putText(image, "No detectado por YOLO estandar", (10, 30), 
                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
    
    # Mostrar imagen
    cv2.imshow("YOLO Test - Medicamentos", image)
    print("💡 Presiona cualquier tecla para cerrar...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def main():
    """Función principal"""
    print("🧪 Probador de YOLO en Medicamentos")
    print("Vamos a ver qué detecta YOLO estándar en tus medicamentos")
    
    while True:
        print("\n📋 Opciones:")
        print("1. 🎯 Prueba rápida (1 imagen)")
        print("2. 🔬 Prueba completa (5 imágenes)")
        print("3. ❌ Salir")
        
        choice = input("\nElige opción (1-3): ").strip()
        
        if choice == '1':
            test_single_image()
        elif choice == '2':
            test_yolo_on_medicamentos()
        elif choice == '3':
            print("👋 ¡Hasta luego!")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
