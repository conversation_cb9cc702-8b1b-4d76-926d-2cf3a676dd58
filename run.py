"""
Script para ejecutar el servidor de desarrollo
"""

import uvicorn
from app.core.config import settings

if __name__ == "__main__":
    print("🚀 Iniciando Mobile Voice Detection API...")
    print(f"📍 Servidor: http://{settings.HOST}:{settings.PORT}")
    print(f"📖 Documentación: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 Modo debug: {settings.DEBUG}")
    print("-" * 50)
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
