"""
Rutas para entrenamiento de modelos YOLO personalizados
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form, BackgroundTasks
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import shutil
import uuid
import zipfile
import yaml
from datetime import datetime

from ...core.config import settings
from ...core.database import get_db, TrainingSession, CustomObject
from sqlalchemy.orm import Session
import json

router = APIRouter()

@router.post("/dataset/upload")
async def upload_dataset(
    dataset_zip: UploadFile = File(...),
    dataset_name: str = Form(...),
    description: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """
    Subir dataset para entrenamiento
    
    Args:
        dataset_zip: Archivo ZIP con dataset
        dataset_name: Nombre del dataset
        description: Descripción opcional
        
    Returns:
        Información del dataset subido
    """
    if not dataset_zip.filename.endswith('.zip'):
        raise HTTPException(status_code=400, detail="El archivo debe ser un ZIP")
    
    try:
        # Crear directorio para el dataset
        dataset_dir = os.path.join(settings.DATASETS_DIR, dataset_name)
        os.makedirs(dataset_dir, exist_ok=True)
        
        # Guardar archivo ZIP
        zip_path = os.path.join(dataset_dir, f"{dataset_name}.zip")
        with open(zip_path, "wb") as buffer:
            shutil.copyfileobj(dataset_zip.file, buffer)
        
        # Extraer ZIP
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(dataset_dir)
        
        # Verificar estructura del dataset
        required_dirs = ['images/train', 'images/val', 'labels/train', 'labels/val']
        missing_dirs = []
        
        for req_dir in required_dirs:
            full_path = os.path.join(dataset_dir, req_dir)
            if not os.path.exists(full_path):
                missing_dirs.append(req_dir)
        
        if missing_dirs:
            return {
                "success": False,
                "error": f"Estructura de dataset incompleta. Faltan: {missing_dirs}",
                "expected_structure": {
                    "images/train/": "Imágenes de entrenamiento",
                    "images/val/": "Imágenes de validación", 
                    "labels/train/": "Etiquetas de entrenamiento (.txt)",
                    "labels/val/": "Etiquetas de validación (.txt)"
                }
            }
        
        # Contar archivos
        train_images = len([f for f in os.listdir(os.path.join(dataset_dir, 'images/train')) 
                           if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
        val_images = len([f for f in os.listdir(os.path.join(dataset_dir, 'images/val')) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
        
        # Crear archivo dataset.yaml
        dataset_config = {
            'path': dataset_dir,
            'train': 'images/train',
            'val': 'images/val',
            'test': 'images/test',  # Opcional
            'nc': 0,  # Se actualizará al analizar las etiquetas
            'names': []  # Se actualizará al analizar las etiquetas
        }
        
        # Analizar etiquetas para obtener clases
        classes = set()
        labels_dir = os.path.join(dataset_dir, 'labels/train')
        for label_file in os.listdir(labels_dir):
            if label_file.endswith('.txt'):
                with open(os.path.join(labels_dir, label_file), 'r') as f:
                    for line in f:
                        if line.strip():
                            class_id = int(line.split()[0])
                            classes.add(class_id)
        
        # Actualizar configuración con clases encontradas
        dataset_config['nc'] = len(classes)
        dataset_config['names'] = [f'class_{i}' for i in sorted(classes)]
        
        # Guardar dataset.yaml
        yaml_path = os.path.join(dataset_dir, 'dataset.yaml')
        with open(yaml_path, 'w') as f:
            yaml.dump(dataset_config, f, default_flow_style=False)
        
        return {
            "success": True,
            "dataset_name": dataset_name,
            "dataset_path": dataset_dir,
            "yaml_path": yaml_path,
            "statistics": {
                "train_images": train_images,
                "val_images": val_images,
                "total_classes": len(classes),
                "class_ids": sorted(classes)
            },
            "config": dataset_config
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando dataset: {str(e)}")

@router.post("/start")
async def start_training(
    dataset_name: str = Form(...),
    model_size: str = Form("n"),  # n, s, m, l, x
    epochs: int = Form(100),
    batch_size: int = Form(16),
    image_size: int = Form(640),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """
    Iniciar entrenamiento de modelo YOLO
    
    Args:
        dataset_name: Nombre del dataset
        model_size: Tamaño del modelo (n, s, m, l, x)
        epochs: Número de épocas
        batch_size: Tamaño del batch
        image_size: Tamaño de imagen
        
    Returns:
        Información de la sesión de entrenamiento
    """
    # Verificar que existe el dataset
    dataset_dir = os.path.join(settings.DATASETS_DIR, dataset_name)
    yaml_path = os.path.join(dataset_dir, 'dataset.yaml')
    
    if not os.path.exists(yaml_path):
        raise HTTPException(status_code=404, detail="Dataset no encontrado")
    
    try:
        # Crear sesión de entrenamiento
        training_session = TrainingSession(
            model_name=f"yolov8{model_size}_{dataset_name}",
            dataset_path=yaml_path,
            epochs=epochs,
            batch_size=batch_size,
            status="pending",
            started_at=datetime.utcnow()
        )
        
        db.add(training_session)
        db.commit()
        db.refresh(training_session)
        
        # Iniciar entrenamiento en background
        background_tasks.add_task(
            run_training,
            training_session.id,
            yaml_path,
            model_size,
            epochs,
            batch_size,
            image_size
        )
        
        return {
            "success": True,
            "training_id": training_session.id,
            "status": "started",
            "message": "Entrenamiento iniciado en segundo plano",
            "parameters": {
                "dataset": dataset_name,
                "model_size": model_size,
                "epochs": epochs,
                "batch_size": batch_size,
                "image_size": image_size
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error iniciando entrenamiento: {str(e)}")

async def run_training(
    training_id: int,
    dataset_yaml: str,
    model_size: str,
    epochs: int,
    batch_size: int,
    image_size: int
):
    """
    Ejecutar entrenamiento en background
    """
    from ...core.database import SessionLocal
    from ultralytics import YOLO
    
    db = SessionLocal()
    
    try:
        # Obtener sesión de entrenamiento
        training_session = db.query(TrainingSession).filter(
            TrainingSession.id == training_id
        ).first()
        
        if not training_session:
            return
        
        # Actualizar estado
        training_session.status = "running"
        db.commit()
        
        # Cargar modelo
        model = YOLO(f'yolov8{model_size}.pt')
        
        # Configurar directorio de salida
        output_dir = os.path.join(settings.MODELS_DIR, f"training_{training_id}")
        os.makedirs(output_dir, exist_ok=True)
        
        # Entrenar modelo
        results = model.train(
            data=dataset_yaml,
            epochs=epochs,
            batch=batch_size,
            imgsz=image_size,
            project=output_dir,
            name='run',
            device='cpu',  # Cambiar a 'cuda' si tienes GPU
            save_period=10,
            patience=20,
            augment=True,
            verbose=True
        )
        
        # Guardar modelo entrenado
        model_path = os.path.join(output_dir, 'run', 'weights', 'best.pt')
        
        # Actualizar sesión
        training_session.status = "completed"
        training_session.model_path = model_path
        training_session.completed_at = datetime.utcnow()
        
        # Obtener métricas finales si están disponibles
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            if 'metrics/mAP50' in metrics:
                training_session.accuracy = metrics['metrics/mAP50']
        
        db.commit()
        
    except Exception as e:
        # Marcar como fallido
        training_session.status = "failed"
        db.commit()
        print(f"Error en entrenamiento {training_id}: {e}")
        
    finally:
        db.close()

@router.get("/status/{training_id}")
async def get_training_status(
    training_id: int,
    db: Session = Depends(get_db)
):
    """
    Obtener estado del entrenamiento
    
    Args:
        training_id: ID de la sesión de entrenamiento
        
    Returns:
        Estado actual del entrenamiento
    """
    training_session = db.query(TrainingSession).filter(
        TrainingSession.id == training_id
    ).first()
    
    if not training_session:
        raise HTTPException(status_code=404, detail="Sesión de entrenamiento no encontrada")
    
    return {
        "training_id": training_session.id,
        "model_name": training_session.model_name,
        "status": training_session.status,
        "progress": training_session.progress,
        "current_epoch": training_session.current_epoch,
        "total_epochs": training_session.epochs,
        "loss_value": training_session.loss_value,
        "accuracy": training_session.accuracy,
        "started_at": training_session.started_at.isoformat() if training_session.started_at else None,
        "completed_at": training_session.completed_at.isoformat() if training_session.completed_at else None,
        "model_path": training_session.model_path
    }

@router.get("/sessions")
async def get_training_sessions(
    limit: int = 10,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Obtener lista de sesiones de entrenamiento
    
    Args:
        limit: Número máximo de resultados
        status: Filtrar por estado
        
    Returns:
        Lista de sesiones
    """
    query = db.query(TrainingSession)
    
    if status:
        query = query.filter(TrainingSession.status == status)
    
    sessions = query.order_by(TrainingSession.created_at.desc()).limit(limit).all()
    
    result = []
    for session in sessions:
        result.append({
            "id": session.id,
            "model_name": session.model_name,
            "status": session.status,
            "epochs": session.epochs,
            "progress": session.progress,
            "accuracy": session.accuracy,
            "created_at": session.created_at.isoformat(),
            "completed_at": session.completed_at.isoformat() if session.completed_at else None
        })
    
    return {
        "success": True,
        "total_sessions": len(result),
        "sessions": result
    }

@router.post("/objects/add")
async def add_custom_object(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    category: Optional[str] = Form("object"),
    db: Session = Depends(get_db)
):
    """
    Agregar objeto personalizado para entrenar
    
    Args:
        name: Nombre del objeto
        description: Descripción
        category: Categoría del objeto
        
    Returns:
        Objeto creado
    """
    # Verificar si ya existe
    existing = db.query(CustomObject).filter(CustomObject.name == name).first()
    if existing:
        raise HTTPException(status_code=400, detail="El objeto ya existe")
    
    try:
        custom_object = CustomObject(
            name=name,
            description=description,
            category=category
        )
        
        db.add(custom_object)
        db.commit()
        db.refresh(custom_object)
        
        return {
            "success": True,
            "object": {
                "id": custom_object.id,
                "name": custom_object.name,
                "description": custom_object.description,
                "category": custom_object.category,
                "created_at": custom_object.created_at.isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creando objeto: {str(e)}")

@router.get("/objects")
async def get_custom_objects(
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Obtener lista de objetos personalizados
    
    Args:
        category: Filtrar por categoría
        
    Returns:
        Lista de objetos
    """
    query = db.query(CustomObject).filter(CustomObject.is_active == True)
    
    if category:
        query = query.filter(CustomObject.category == category)
    
    objects = query.order_by(CustomObject.created_at.desc()).all()
    
    result = []
    for obj in objects:
        result.append({
            "id": obj.id,
            "name": obj.name,
            "description": obj.description,
            "category": obj.category,
            "training_images_count": obj.training_images_count,
            "created_at": obj.created_at.isoformat()
        })
    
    return {
        "success": True,
        "total_objects": len(result),
        "objects": result
    }

@router.get("/model/{training_id}/download")
async def download_trained_model(
    training_id: int,
    db: Session = Depends(get_db)
):
    """
    Descargar modelo entrenado
    
    Args:
        training_id: ID de la sesión de entrenamiento
        
    Returns:
        Archivo del modelo
    """
    training_session = db.query(TrainingSession).filter(
        TrainingSession.id == training_id
    ).first()
    
    if not training_session:
        raise HTTPException(status_code=404, detail="Sesión de entrenamiento no encontrada")
    
    if training_session.status != "completed":
        raise HTTPException(status_code=400, detail="El entrenamiento no ha completado")
    
    if not training_session.model_path or not os.path.exists(training_session.model_path):
        raise HTTPException(status_code=404, detail="Archivo del modelo no encontrado")
    
    return FileResponse(
        training_session.model_path,
        media_type="application/octet-stream",
        filename=f"{training_session.model_name}.pt"
    )
