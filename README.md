# Mobile Voice Detection API

Backend completo para aplicación móvil con detección de voz y YOLO que puede detectar **TODO**: objetos, texto, pastillas, medicamentos, etc.

## 🚀 Características

### Detección Visual
- **YOLO v8**: Detección de objetos en tiempo real
- **OCR Avanzado**: Reconocimiento de texto con EasyOCR y Tesseract
- **Detección Combinada**: Objetos + texto en una sola llamada
- **Búsqueda Específica**: Buscar objetos particulares
- **Anotaciones**: Imágenes con detecciones marcadas

### Comandos de Voz
- **Speech-to-Text**: Whisper (offline) + Google (online)
- **Text-to-Speech**: Síntesis de voz local
- **Comandos Inteligentes**: Procesamiento de lenguaje natural
- **Sesiones Interactivas**: Conversación completa con respuestas en audio

### Entrenamiento Personalizado
- **YOLO Personalizado**: Entrenar modelos para objetos específicos
- **Gestión de Datasets**: Subir y organizar datos de entrenamiento
- **Monitoreo**: Seguimiento del progreso de entrenamiento
- **Exportación**: Modelos optimizados para móviles

## 📦 Instalación

### 1. Clonar repositorio
```bash
git clone <tu-repo>
cd mobile-voice-detection
```

### 2. Crear entorno virtual
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# o
venv\Scripts\activate  # Windows
```

### 3. Instalar dependencias
```bash
pip install -r requirements.txt
```

### 4. Configurar variables de entorno
```bash
cp .env.example .env
# Editar .env según tus necesidades
```

### 5. Ejecutar servidor
```bash
python main.py
```

El servidor estará disponible en: `http://localhost:8000`

## 🎯 Endpoints Principales

### Detección de Objetos
```bash
# Detectar todos los objetos
curl -X POST "http://localhost:8000/api/v1/detection/objects" \
  -F "image=@imagen.jpg"

# Buscar objetos específicos
curl -X POST "http://localhost:8000/api/v1/detection/search" \
  -F "image=@imagen.jpg" \
  -F "targets=persona,carro,perro"
```

### Reconocimiento de Texto
```bash
# Extraer texto de imagen
curl -X POST "http://localhost:8000/api/v1/detection/text" \
  -F "image=@documento.jpg" \
  -F "method=easyocr"
```

### Comandos de Voz
```bash
# Procesar comando de voz con imagen
curl -X POST "http://localhost:8000/api/v1/voice/command" \
  -F "audio=@comando.wav" \
  -F "image=@imagen.jpg"

# Sesión interactiva (devuelve respuesta en audio)
curl -X POST "http://localhost:8000/api/v1/voice/interactive" \
  -F "audio=@comando.wav" \
  -F "image=@imagen.jpg"
```

### Entrenamiento
```bash
# Subir dataset
curl -X POST "http://localhost:8000/api/v1/training/dataset/upload" \
  -F "dataset_zip=@mi_dataset.zip" \
  -F "dataset_name=pastillas_dataset"

# Iniciar entrenamiento
curl -X POST "http://localhost:8000/api/v1/training/start" \
  -F "dataset_name=pastillas_dataset" \
  -F "epochs=100"
```

## 🗣️ Comandos de Voz Disponibles

| Comando | Ejemplo | Descripción |
|---------|---------|-------------|
| `detectar objetos` | "¿Qué ves en la imagen?" | Detecta todos los objetos |
| `leer texto` | "¿Qué dice el documento?" | Extrae texto de la imagen |
| `buscar [objeto]` | "¿Hay personas en la foto?" | Busca objetos específicos |
| `contar [objetos]` | "¿Cuántos carros hay?" | Cuenta objetos |
| `ayuda` | "¿Qué puedes hacer?" | Muestra comandos disponibles |

## 📊 Estructura del Proyecto

```
mobile-voice-detection/
├── app/
│   ├── api/
│   │   └── routes/
│   │       ├── detection.py    # Endpoints de detección
│   │       ├── voice.py        # Endpoints de voz
│   │       └── training.py     # Endpoints de entrenamiento
│   ├── core/
│   │   ├── config.py          # Configuración
│   │   └── database.py        # Base de datos SQLite
│   └── services/
│       ├── yolo_service.py    # Servicio YOLO
│       ├── ocr_service.py     # Servicio OCR
│       └── voice_service.py   # Servicio de voz
├── datasets/                  # Datasets para entrenamiento
├── models/                    # Modelos entrenados
├── uploads/                   # Archivos subidos
├── temp/                      # Archivos temporales
├── main.py                    # Servidor principal
└── requirements.txt           # Dependencias
```

## 🔧 Configuración Avanzada

### GPU (Recomendado para entrenamiento)
```bash
# Instalar PyTorch con CUDA
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Actualizar configuración
OCR_GPU=True
```

### Modelos Personalizados
1. Preparar dataset en formato YOLO
2. Subir via API o colocar en `datasets/`
3. Iniciar entrenamiento
4. Usar modelo entrenado para detección

## 📱 Integración Móvil

### React Native
```javascript
// Detectar objetos
const detectObjects = async (imageUri) => {
  const formData = new FormData();
  formData.append('image', {
    uri: imageUri,
    type: 'image/jpeg',
    name: 'image.jpg'
  });
  
  const response = await fetch('http://tu-servidor:8000/api/v1/detection/objects', {
    method: 'POST',
    body: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
  
  return await response.json();
};

// Comando de voz
const processVoiceCommand = async (audioUri, imageUri) => {
  const formData = new FormData();
  formData.append('audio', { uri: audioUri, type: 'audio/wav', name: 'audio.wav' });
  formData.append('image', { uri: imageUri, type: 'image/jpeg', name: 'image.jpg' });
  
  const response = await fetch('http://tu-servidor:8000/api/v1/voice/interactive', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};
```

## 🚀 Próximos Pasos

1. **Instalar dependencias**: `pip install -r requirements.txt`
2. **Ejecutar servidor**: `python main.py`
3. **Probar endpoints**: Usar la documentación automática en `http://localhost:8000/docs`
4. **Entrenar modelo personalizado**: Subir tu dataset y entrenar
5. **Integrar con móvil**: Usar los endpoints desde tu app

## 📚 Documentación API

Una vez ejecutando el servidor, visita:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🤝 Contribuir

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.
