"""
Extractor simple para pastillas.yolov11.zip
"""

import zipfile
import os
import shutil

def extract_pastillas_zip():
    """Extraer el archivo de pastillas"""
    zip_file = "pastillas.yolov11.zip"
    
    if not os.path.exists(zip_file):
        print(f"❌ No se encuentra: {zip_file}")
        return
    
    print(f"📦 Extrayendo {zip_file}...")
    
    # Crear directorio de destino
    dest_dir = "datasets/manual_medicamentos/raw_images"
    os.makedirs(dest_dir, exist_ok=True)
    
    try:
        # Abrir ZIP
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            # Listar archivos
            all_files = zip_ref.namelist()
            print(f"📋 Total archivos en ZIP: {len(all_files)}")
            
            # Filtrar solo imágenes
            image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
            image_files = []
            
            for file in all_files:
                if file.lower().endswith(image_extensions):
                    image_files.append(file)
            
            print(f"📸 Imágenes encontradas: {len(image_files)}")
            
            if len(image_files) == 0:
                print("⚠️ No se encontraron imágenes en el ZIP")
                # Mostrar algunos archivos para debug
                print("📋 Primeros 10 archivos:")
                for i, file in enumerate(all_files[:10]):
                    print(f"   {i+1}. {file}")
                return
            
            # Extraer a directorio temporal
            temp_dir = "temp_pastillas"
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            print("🔓 Extrayendo archivos...")
            zip_ref.extractall(temp_dir)
            
            # Mover imágenes al destino final
            moved_count = 0
            
            for img_file in image_files:
                src_path = os.path.join(temp_dir, img_file)
                
                if os.path.exists(src_path):
                    # Obtener solo el nombre del archivo
                    filename = os.path.basename(img_file)
                    
                    # Asegurar que tenga prefijo de pastilla
                    if not filename.lower().startswith('pastilla'):
                        filename = f"pastilla_{moved_count:04d}_{filename}"
                    
                    dst_path = os.path.join(dest_dir, filename)
                    
                    # Mover archivo
                    shutil.move(src_path, dst_path)
                    moved_count += 1
                    
                    print(f"✅ {filename}")
                else:
                    print(f"⚠️ No encontrado: {src_path}")
            
            # Limpiar directorio temporal
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            print(f"\n🎉 ¡Extracción completada!")
            print(f"📊 {moved_count} imágenes extraídas")
            print(f"📁 Ubicación: {dest_dir}")
            
            # Verificar resultado
            final_count = len([f for f in os.listdir(dest_dir) 
                             if f.lower().endswith(image_extensions)])
            print(f"✅ Verificación: {final_count} imágenes en el dataset")
            
            if final_count >= 30:
                print("\n🎯 ¡Dataset listo para anotar!")
                print("📋 Próximos pasos:")
                print("1. 🌐 Ve a: https://roboflow.com/")
                print("2. 📤 Sube las imágenes")
                print("3. 🏷️ Anota cada pastilla con bounding boxes")
                print("4. 📥 Exporta en formato YOLOv8")
                print("5. 🤖 Entrena: python train_custom_yolo.py")
            else:
                print(f"\n⚠️ Recomendación: Agrega más imágenes")
                print(f"   Tienes: {final_count} imágenes")
                print(f"   Recomendado: 50+ imágenes")
            
    except Exception as e:
        print(f"❌ Error durante extracción: {e}")

if __name__ == "__main__":
    extract_pastillas_zip()
