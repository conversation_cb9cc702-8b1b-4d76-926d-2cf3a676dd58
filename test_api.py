"""
Script de prueba para la API
"""

import requests
import json
import os

# Configuración
BASE_URL = "http://localhost:8000"
API_V1 = f"{BASE_URL}/api/v1"

def test_health():
    """Probar endpoint de salud"""
    print("🔍 Probando endpoint de salud...")
    
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_yolo_classes():
    """Probar obtención de clases YOLO"""
    print("🏷️ Probando clases YOLO disponibles...")
    
    response = requests.get(f"{API_V1}/detection/classes")
    if response.status_code == 200:
        data = response.json()
        print(f"Total clases: {data['total_classes']}")
        print(f"Primeras 10 clases: {data['classes'][:10]}")
    else:
        print(f"Error: {response.status_code}")
    print("-" * 50)

def test_object_detection(image_path):
    """Probar detección de objetos"""
    if not os.path.exists(image_path):
        print(f"❌ Imagen no encontrada: {image_path}")
        return
    
    print(f"🤖 Probando detección de objetos con: {image_path}")
    
    with open(image_path, 'rb') as f:
        files = {'image': f}
        response = requests.post(f"{API_V1}/detection/objects", files=files)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Objetos detectados: {data['total_objects']}")
        for obj in data.get('detections', [])[:5]:  # Mostrar primeros 5
            print(f"  - {obj['class_name']}: {obj['confidence']:.2f}")
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)
    print("-" * 50)

def test_text_detection(image_path):
    """Probar detección de texto"""
    if not os.path.exists(image_path):
        print(f"❌ Imagen no encontrada: {image_path}")
        return
    
    print(f"📖 Probando detección de texto con: {image_path}")
    
    with open(image_path, 'rb') as f:
        files = {'image': f}
        data = {'method': 'easyocr'}
        response = requests.post(f"{API_V1}/detection/text", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Texto extraído: {len(result.get('full_text', ''))}")
        print(f"Texto: {result.get('full_text', '')[:100]}...")
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)
    print("-" * 50)

def test_voice_transcription(audio_path):
    """Probar transcripción de voz"""
    if not os.path.exists(audio_path):
        print(f"❌ Audio no encontrado: {audio_path}")
        return
    
    print(f"🎤 Probando transcripción de voz con: {audio_path}")
    
    with open(audio_path, 'rb') as f:
        files = {'audio': f}
        data = {'method': 'whisper'}
        response = requests.post(f"{API_V1}/voice/transcribe", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Texto transcrito: {result.get('transcribed_text', '')}")
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)
    print("-" * 50)

def test_available_commands():
    """Probar comandos disponibles"""
    print("📋 Probando comandos de voz disponibles...")
    
    response = requests.get(f"{API_V1}/voice/commands/available")
    if response.status_code == 200:
        data = response.json()
        print(f"Total comandos: {data['total_commands']}")
        for cmd, info in data['available_commands'].items():
            print(f"  - {cmd}: {info['description']}")
    else:
        print(f"❌ Error: {response.status_code}")
    print("-" * 50)

def main():
    """Ejecutar todas las pruebas"""
    print("🧪 Iniciando pruebas de la API...")
    print("=" * 50)
    
    # Pruebas básicas
    test_health()
    test_yolo_classes()
    test_available_commands()
    
    # Pruebas con archivos (opcional)
    # Crear archivos de prueba o usar existentes
    test_image = "test_image.jpg"  # Cambiar por ruta real
    test_audio = "test_audio.wav"  # Cambiar por ruta real
    
    if os.path.exists(test_image):
        test_object_detection(test_image)
        test_text_detection(test_image)
    else:
        print(f"⚠️ Imagen de prueba no encontrada: {test_image}")
        print("   Coloca una imagen en la raíz del proyecto para probar detección")
    
    if os.path.exists(test_audio):
        test_voice_transcription(test_audio)
    else:
        print(f"⚠️ Audio de prueba no encontrado: {test_audio}")
        print("   Coloca un archivo de audio para probar transcripción")
    
    print("✅ Pruebas completadas!")

if __name__ == "__main__":
    main()
