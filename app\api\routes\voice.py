"""
Rutas para comandos de voz
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import FileResponse
from typing import Optional
import os
import shutil
import uuid
from datetime import datetime

from ...core.config import settings
from ...core.database import get_db, VoiceCommand
from ...services.voice_service import VoiceService
from ...services.yolo_service import YOLOService
from ...services.ocr_service import OCRService
from sqlalchemy.orm import Session
import json

router = APIRouter()

def save_uploaded_audio(upload_file: UploadFile, directory: str) -> str:
    """Guardar archivo de audio subido"""
    # Generar nombre único
    file_extension = os.path.splitext(upload_file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(directory, unique_filename)
    
    # Guardar archivo
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)
    
    return file_path

def save_uploaded_image(upload_file: UploadFile, directory: str) -> str:
    """Guardar archivo de imagen subido"""
    # Generar nombre único
    file_extension = os.path.splitext(upload_file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(directory, unique_filename)
    
    # Guardar archivo
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)
    
    return file_path

@router.post("/transcribe")
async def transcribe_audio(
    audio: UploadFile = File(...),
    method: str = "whisper",  # whisper, google
    voice_service: VoiceService = Depends(),
    db: Session = Depends(get_db)
):
    """
    Transcribir audio a texto
    
    Args:
        audio: Archivo de audio
        method: Método de transcripción (whisper, google)
        
    Returns:
        Texto transcrito
    """
    # Validar archivo
    if not audio.filename.lower().endswith(tuple(settings.ALLOWED_AUDIO_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de audio no válido")
    
    if audio.size > settings.MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="Archivo demasiado grande")
    
    try:
        # Guardar audio
        audio_path = save_uploaded_audio(audio, settings.UPLOAD_DIR)
        
        # Transcribir según método
        if method == "whisper":
            results = voice_service.speech_to_text_whisper(audio_path)
        elif method == "google":
            results = voice_service.speech_to_text_google(audio_path)
        else:
            raise HTTPException(status_code=400, detail="Método de transcripción no válido")
        
        # Guardar en base de datos
        db_command = VoiceCommand(
            audio_path=audio_path,
            transcribed_text=results.get("transcribed_text", ""),
            command_type="transcription",
            processing_time=results.get("processing_time", 0)
        )
        db.add(db_command)
        db.commit()
        
        results["db_id"] = db_command.id
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error transcribiendo audio: {str(e)}")

@router.post("/command")
async def process_voice_command(
    audio: UploadFile = File(...),
    image: Optional[UploadFile] = File(None),
    voice_service: VoiceService = Depends(),
    yolo_service: YOLOService = Depends(),
    ocr_service: OCRService = Depends(),
    db: Session = Depends(get_db)
):
    """
    Procesar comando de voz completo con imagen opcional
    
    Args:
        audio: Archivo de audio con comando
        image: Imagen opcional para procesar
        
    Returns:
        Resultado del comando ejecutado
    """
    # Validar archivos
    if not audio.filename.lower().endswith(tuple(settings.ALLOWED_AUDIO_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de audio no válido")
    
    if image and not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")
    
    try:
        # Guardar audio
        audio_path = save_uploaded_audio(audio, settings.UPLOAD_DIR)
        
        # Procesar comando de voz
        voice_result = voice_service.process_voice_command(audio_path)
        
        if not voice_result["success"]:
            return voice_result
        
        command = voice_result["command"]
        command_type = command["command_type"]
        
        # Ejecutar comando según tipo
        execution_result = None
        response_text = ""
        
        if image:
            # Guardar imagen
            image_path = save_uploaded_image(image, settings.UPLOAD_DIR)
            
            if command_type == "detect_objects":
                execution_result = yolo_service.detect_objects(image_path)
                response_text = voice_service.get_response_text(command_type, execution_result)
                
            elif command_type == "detect_text":
                execution_result = ocr_service.extract_text_easyocr(image_path)
                response_text = voice_service.get_response_text(command_type, execution_result)
                
            elif command_type == "search_object":
                target = command.get("parameters", {}).get("target", "objeto")
                execution_result = yolo_service.search_objects(image_path, [target])
                execution_result["search_targets"] = [target]
                response_text = voice_service.get_response_text(command_type, execution_result)
                
            elif command_type == "count_objects":
                execution_result = yolo_service.detect_objects(image_path)
                response_text = voice_service.get_response_text(command_type, execution_result)
                
            else:
                response_text = "Necesito una imagen para ejecutar este comando"
        else:
            if command_type == "help":
                response_text = voice_service.get_response_text(command_type, {})
            else:
                response_text = "Necesito una imagen para ejecutar este comando"
        
        # Guardar en base de datos
        db_command = VoiceCommand(
            audio_path=audio_path,
            transcribed_text=voice_result["transcription"]["transcribed_text"],
            command_type=command_type,
            parameters=json.dumps(command.get("parameters", {})),
            response_text=response_text,
            processing_time=voice_result["processing_time"]
        )
        db.add(db_command)
        db.commit()
        
        # Preparar respuesta completa
        result = {
            "success": True,
            "voice_processing": voice_result,
            "command_execution": execution_result,
            "response_text": response_text,
            "db_id": db_command.id
        }
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando comando: {str(e)}")

@router.post("/speak")
async def text_to_speech(
    text: str = Form(...),
    save_file: bool = False,
    voice_service: VoiceService = Depends()
):
    """
    Convertir texto a voz
    
    Args:
        text: Texto a convertir
        save_file: Si guardar archivo de audio
        
    Returns:
        Resultado de síntesis de voz
    """
    try:
        if save_file:
            # Generar nombre único para archivo
            audio_filename = f"{uuid.uuid4()}.wav"
            audio_path = os.path.join(settings.TEMP_DIR, audio_filename)
            
            result = voice_service.text_to_speech(text, audio_path)
            
            if result["success"]:
                result["audio_url"] = f"/api/v1/voice/audio/{audio_filename}"
            
            return result
        else:
            # Reproducir directamente (no útil para API, pero incluido)
            result = voice_service.text_to_speech(text)
            return result
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error en síntesis de voz: {str(e)}")

@router.get("/audio/{filename}")
async def get_audio(filename: str):
    """
    Obtener archivo de audio generado
    
    Args:
        filename: Nombre del archivo
        
    Returns:
        Archivo de audio
    """
    file_path = os.path.join(settings.TEMP_DIR, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Archivo de audio no encontrado")
    
    return FileResponse(file_path, media_type="audio/wav")

@router.post("/interactive")
async def interactive_session(
    audio: UploadFile = File(...),
    image: Optional[UploadFile] = File(None),
    session_id: Optional[str] = Form(None),
    voice_service: VoiceService = Depends(),
    yolo_service: YOLOService = Depends(),
    ocr_service: OCRService = Depends(),
    db: Session = Depends(get_db)
):
    """
    Sesión interactiva: procesa comando y devuelve respuesta en audio
    
    Args:
        audio: Comando de voz
        image: Imagen opcional
        session_id: ID de sesión (opcional)
        
    Returns:
        Respuesta completa con audio
    """
    try:
        # Procesar comando de voz
        command_result = await process_voice_command(
            audio=audio,
            image=image,
            voice_service=voice_service,
            yolo_service=yolo_service,
            ocr_service=ocr_service,
            db=db
        )
        
        if not command_result["success"]:
            return command_result
        
        # Generar respuesta en audio
        response_text = command_result["response_text"]
        audio_filename = f"{uuid.uuid4()}.wav"
        audio_path = os.path.join(settings.TEMP_DIR, audio_filename)
        
        tts_result = voice_service.text_to_speech(response_text, audio_path)
        
        if tts_result["success"]:
            command_result["response_audio_url"] = f"/api/v1/voice/audio/{audio_filename}"
        
        return command_result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error en sesión interactiva: {str(e)}")

@router.get("/commands/history")
async def get_voice_commands_history(
    limit: int = 10,
    command_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Obtener historial de comandos de voz
    
    Args:
        limit: Número máximo de resultados
        command_type: Filtrar por tipo de comando
        
    Returns:
        Historial de comandos
    """
    try:
        query = db.query(VoiceCommand)
        
        if command_type:
            query = query.filter(VoiceCommand.command_type == command_type)
        
        results = query.order_by(VoiceCommand.created_at.desc()).limit(limit).all()
        
        history = []
        for result in results:
            history.append({
                "id": result.id,
                "transcribed_text": result.transcribed_text,
                "command_type": result.command_type,
                "response_text": result.response_text,
                "processing_time": result.processing_time,
                "created_at": result.created_at.isoformat()
            })
        
        return {
            "success": True,
            "total_results": len(history),
            "history": history
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo historial: {str(e)}")

@router.get("/commands/available")
async def get_available_commands():
    """
    Obtener lista de comandos disponibles
    
    Returns:
        Lista de comandos y ejemplos
    """
    commands = {
        "detect_objects": {
            "description": "Detectar objetos en una imagen",
            "examples": ["detectar objetos", "que ves", "analizar imagen"],
            "requires_image": True
        },
        "detect_text": {
            "description": "Leer texto en una imagen",
            "examples": ["leer texto", "que dice", "extraer texto"],
            "requires_image": True
        },
        "search_object": {
            "description": "Buscar un objeto específico",
            "examples": ["buscar persona", "encontrar carro", "hay perros"],
            "requires_image": True
        },
        "count_objects": {
            "description": "Contar objetos en una imagen",
            "examples": ["contar objetos", "cuantos hay", "numero de personas"],
            "requires_image": True
        },
        "help": {
            "description": "Obtener ayuda sobre comandos",
            "examples": ["ayuda", "que puedes hacer", "comandos"],
            "requires_image": False
        }
    }
    
    return {
        "success": True,
        "available_commands": commands,
        "total_commands": len(commands)
    }
