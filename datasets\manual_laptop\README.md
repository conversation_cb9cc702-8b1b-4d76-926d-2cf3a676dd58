# Dataset Manual: Laptop

## 📁 Estructura Creada:
```
manual_laptop/
├── raw_images/          ← AGREGA TUS IMÁGENES AQUÍ
├── images/
│   ├── train/          ← Imágenes de entrenamiento (después de anotar)
│   ├── val/            ← Imágenes de validación
│   └── test/           ← Imágenes de prueba
└── labels/
    ├── train/          ← Etiquetas de entrenamiento
    ├── val/            ← Etiquetas de validación
    └── test/           ← Etiquetas de prueba
```

## 🎯 Pasos a Seguir:

### 1. 📸 Agregar Imágenes (MANUAL)
- Copia 50-100 imágenes de laptop en `raw_images/`
- Usa fotos de tu teléfono, internet, o toma nuevas
- Diferentes ángulos, iluminación, fondos

### 2. 🏷️ Anotar en Roboflow
1. Ve a: https://roboflow.com/
2. Crea cuenta gratuita
3. Sube todas las imágenes de `raw_images/`
4. Anota cada laptop con bounding boxes
5. Divide: 70% train, 20% val, 10% test
6. Exporta en formato "YOLOv8"

### 3. 📥 Descargar Dataset Anotado
- Descarga el ZIP de Roboflow
- Extrae en la carpeta del dataset
- Debe crear automáticamente images/ y labels/

### 4. 🤖 Entrenar Modelo
```bash
python train_custom_yolo.py
```

## 💡 Consejos para Laptop:

- 💻 Abierta y cerrada
- 💡 Pantalla encendida/apagada
- 🔌 Con y sin cables
- 📐 Diferentes ángulos
- 🏠 Diferentes ubicaciones


## 🎉 Resultado Esperado:
- 🎯 Detección 95%+ precisa de laptop
- ❌ Menos falsos positivos
- 🚀 Modelo personalizado optimizado
