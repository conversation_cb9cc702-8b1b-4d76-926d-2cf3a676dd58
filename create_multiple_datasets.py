"""
Creador masivo de datasets - <PERSON><PERSON><PERSON> múltiples datasets de una vez
"""

import os
import json
from datetime import datetime

def create_dataset_structure(object_name, base_dir="datasets"):
    """Crear estructura de dataset para un objeto"""
    dataset_name = f"manual_{object_name}"
    dataset_path = os.path.join(base_dir, dataset_name)
    
    # Directorios a crear
    directories = [
        f"{dataset_path}/raw_images",
        f"{dataset_path}/images/train", 
        f"{dataset_path}/images/val",
        f"{dataset_path}/images/test",
        f"{dataset_path}/labels/train",
        f"{dataset_path}/labels/val", 
        f"{dataset_path}/labels/test"
    ]
    
    # Crear directorios
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # Crear configuración
    config = {
        "object_name": object_name,
        "dataset_name": dataset_name,
        "method": "manual",
        "created_at": datetime.now().isoformat(),
        "status": "ready_for_images",
        "target_images": "50-100",
        "instructions": {
            "step_1": f"Agrega 50-100 imágenes de {object_name} en raw_images/",
            "step_2": "Sube a Roboflow.com para anotar",
            "step_3": "Descarga dataset anotado",
            "step_4": "Ejecuta train_custom_yolo.py"
        }
    }
    
    config_path = os.path.join(dataset_path, "config.json")
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    return dataset_path

def create_multiple_datasets():
    """Crear múltiples datasets de una vez"""
    print("🚀 Creador Masivo de Datasets")
    print("=" * 50)
    
    # Lista de objetos importantes para detectar
    objects_list = [
        # Seguridad y vigilancia
        "personas_con_armas",
        "armas_de_fuego", 
        "cuchillos",
        "personas_sospechosas",
        
        # Transporte
        "tren",
        "autos",
        "motos", 
        "bicicletas",
        "camiones",
        "autobuses",
        "aviones",
        "barcos",
        
        # Naturaleza
        "plantas",
        "arboles",
        "flores",
        "animales_domesticos",
        "animales_salvajes",
        
        # Objetos cotidianos
        "personas",
        "rostros",
        "manos",
        "comida",
        "bebidas",
        "libros",
        "herramientas",
        
        # Tecnología
        "computadoras",
        "televisores",
        "camaras",
        "drones",
        
        # Hogar
        "muebles",
        "electrodomesticos",
        "ropa",
        "zapatos",
        
        # Médico/Salud
        "medicamentos",
        "jeringas",
        "mascarillas",
        "equipos_medicos",
        
        # Documentos
        "documentos_identidad",
        "billetes",
        "tarjetas",
        "codigos_qr",
        
        # Deportes
        "pelotas_deportivas",
        "equipos_deportivos",
        
        # Construcción
        "materiales_construccion",
        "maquinaria_pesada",
        
        # Emergencias
        "fuego",
        "humo",
        "inundaciones",
        "accidentes"
    ]
    
    print(f"📋 Creando {len(objects_list)} datasets:")
    print("=" * 50)
    
    created_datasets = []
    failed_datasets = []
    
    for obj in objects_list:
        try:
            dataset_path = create_dataset_structure(obj)
            created_datasets.append((obj, dataset_path))
            print(f"✅ {obj:<25} → {dataset_path}")
        except Exception as e:
            failed_datasets.append((obj, str(e)))
            print(f"❌ {obj:<25} → Error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 RESUMEN:")
    print(f"✅ Creados exitosamente: {len(created_datasets)}")
    print(f"❌ Fallaron: {len(failed_datasets)}")
    
    if failed_datasets:
        print(f"\n❌ Datasets que fallaron:")
        for obj, error in failed_datasets:
            print(f"   - {obj}: {error}")
    
    print(f"\n📁 Todos los datasets están en: datasets/")
    print(f"🎯 Para cada objeto, agrega 50-100 imágenes en: manual_[objeto]/raw_images/")
    
    # Crear archivo de resumen
    summary = {
        "created_at": datetime.now().isoformat(),
        "total_datasets": len(created_datasets),
        "successful": [obj for obj, _ in created_datasets],
        "failed": failed_datasets,
        "instructions": {
            "next_steps": [
                "1. Agrega imágenes en cada carpeta raw_images/",
                "2. Ve a Roboflow.com para anotar",
                "3. Descarga datasets anotados", 
                "4. Entrena modelos con train_custom_yolo.py"
            ]
        }
    }
    
    with open("datasets/datasets_summary.json", 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"📄 Resumen guardado en: datasets/datasets_summary.json")
    
    return created_datasets

def create_priority_datasets():
    """Crear solo los datasets más importantes primero"""
    print("🎯 Creador de Datasets Prioritarios")
    print("=" * 50)
    
    # Objetos más importantes/útiles
    priority_objects = [
        "personas_con_armas",
        "armas_de_fuego",
        "tren", 
        "autos",
        "motos",
        "plantas",
        "personas",
        "rostros",
        "documentos_identidad",
        "medicamentos",
        "fuego",
        "accidentes"
    ]
    
    print(f"📋 Creando {len(priority_objects)} datasets prioritarios:")
    
    created = []
    for obj in priority_objects:
        try:
            dataset_path = create_dataset_structure(obj)
            created.append((obj, dataset_path))
            print(f"✅ {obj}")
        except Exception as e:
            print(f"❌ {obj}: {e}")
    
    print(f"\n✅ {len(created)} datasets prioritarios creados")
    return created

def show_usage_instructions():
    """Mostrar instrucciones de uso"""
    print("\n📋 INSTRUCCIONES DE USO:")
    print("=" * 50)
    
    print("🎯 PARA CADA DATASET:")
    print("1. 📁 Ve a: datasets/manual_[objeto]/raw_images/")
    print("2. 📸 Agrega 50-100 imágenes del objeto")
    print("3. 🌐 Ve a: https://roboflow.com/")
    print("4. 📤 Sube las imágenes")
    print("5. 🏷️ Anota cada objeto con bounding boxes")
    print("6. 📊 Divide: 70% train, 20% val, 10% test")
    print("7. 📥 Exporta en formato 'YOLOv8'")
    print("8. 📂 Descarga y extrae en la carpeta del dataset")
    print("9. 🤖 Entrena: python train_custom_yolo.py")
    
    print("\n💡 FUENTES DE IMÁGENES:")
    print("- 📱 Fotos con tu teléfono")
    print("- 🌐 Google Images (descargar)")
    print("- 📹 Capturas de video")
    print("- 🎥 Frames de películas/documentales")
    print("- 📷 Fotos de internet (Unsplash, Pixabay)")
    
    print("\n🎯 CONSEJOS ESPECÍFICOS:")
    print("🔫 Armas: Diferentes tipos, ángulos, en manos")
    print("🚗 Autos: Diferentes marcas, colores, ángulos")
    print("🏍️ Motos: Deportivas, cruiser, scooters")
    print("🚂 Trenes: Pasajeros, carga, metro, diferentes vistas")
    print("🌱 Plantas: Interior, exterior, diferentes especies")
    print("👤 Personas: Diferentes edades, ropa, poses")
    print("🆔 Documentos: Cédulas, pasaportes, licencias")
    print("🔥 Fuego: Incendios, fogatas, diferentes intensidades")

def main():
    """Función principal"""
    print("🚀 Creador Masivo de Datasets para YOLO")
    print("Crear múltiples datasets de una vez")
    
    while True:
        print("\n📋 Opciones:")
        print("1. 🎯 Crear datasets prioritarios (12 objetos)")
        print("2. 🌟 Crear TODOS los datasets (40+ objetos)")
        print("3. 📋 Ver instrucciones de uso")
        print("4. ❌ Salir")
        
        choice = input("\nElige opción (1-4): ").strip()
        
        if choice == '1':
            create_priority_datasets()
            show_usage_instructions()
        elif choice == '2':
            create_multiple_datasets()
            show_usage_instructions()
        elif choice == '3':
            show_usage_instructions()
        elif choice == '4':
            print("👋 ¡Hasta luego!")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
