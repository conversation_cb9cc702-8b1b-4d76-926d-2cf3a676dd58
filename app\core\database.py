"""
Configuración de base de datos SQLite
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
from .config import settings

# Crear engine de SQLite
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False},  # Necesario para SQLite
    echo=settings.DEBUG  # Mostrar queries SQL en debug
)

# Crear session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base para modelos
Base = declarative_base()

# Modelos de base de datos
class DetectionResult(Base):
    """Resultados de detección YOLO"""
    __tablename__ = "detection_results"
    
    id = Column(Integer, primary_key=True, index=True)
    image_path = Column(String, nullable=False)
    detection_type = Column(String, nullable=False)  # 'yolo', 'ocr', 'combined'
    objects_detected = Column(Text)  # JSON string con objetos detectados
    confidence_scores = Column(Text)  # JSON string con scores
    processing_time = Column(Float)  # Tiempo de procesamiento en segundos
    created_at = Column(DateTime, default=datetime.utcnow)

class VoiceCommand(Base):
    """Comandos de voz procesados"""
    __tablename__ = "voice_commands"
    
    id = Column(Integer, primary_key=True, index=True)
    audio_path = Column(String, nullable=False)
    transcribed_text = Column(Text)
    command_type = Column(String)  # 'detection', 'search', 'count', etc.
    parameters = Column(Text)  # JSON string con parámetros del comando
    response_text = Column(Text)
    processing_time = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)

class TrainingSession(Base):
    """Sesiones de entrenamiento de modelos"""
    __tablename__ = "training_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String, nullable=False)
    dataset_path = Column(String, nullable=False)
    epochs = Column(Integer, default=100)
    batch_size = Column(Integer, default=16)
    status = Column(String, default="pending")  # pending, running, completed, failed
    progress = Column(Float, default=0.0)  # 0-100
    current_epoch = Column(Integer, default=0)
    loss_value = Column(Float)
    accuracy = Column(Float)
    model_path = Column(String)  # Ruta del modelo entrenado
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

class CustomObject(Base):
    """Objetos personalizados para entrenar"""
    __tablename__ = "custom_objects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True)
    description = Column(Text)
    category = Column(String)  # 'medicine', 'document', 'object', etc.
    is_active = Column(Boolean, default=True)
    training_images_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)

# Función para crear todas las tablas
def create_tables():
    """Crear todas las tablas en la base de datos"""
    Base.metadata.create_all(bind=engine)
    print("✅ Tablas de base de datos creadas/verificadas")

# Dependency para obtener sesión de DB
def get_db() -> Session:
    """Obtener sesión de base de datos"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
