"""
YOLO detección en tiempo real con cámara web
Detecta TODO en vivo: personas, objetos, etc.
"""

import cv2
import numpy as np
from ultralytics import YOLO
import time

def main():
    print("🚀 Iniciando YOLO detección en tiempo real...")
    
    # Cargar modelo YOLO
    print("🤖 Cargando modelo YOLO...")
    model = YOLO('yolov8n.pt')  # Puedes cambiar a yolov8s.pt, yolov8m.pt para mejor precisión
    print("✅ Modelo YOLO cargado")
    
    # Inicializar cámara
    print("📹 Iniciando cámara...")
    cap = cv2.VideoCapture(0)  # 0 para cámara por defecto
    
    if not cap.isOpened():
        print("❌ Error: No se pudo abrir la cámara")
        print("💡 Asegúrate de que:")
        print("   - La cámara esté conectada")
        print("   - No esté siendo usada por otra aplicación")
        print("   - Tengas permisos de cámara")
        return
    
    # Configurar cámara
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    print("✅ Cámara iniciada")
    print("\n🎯 YOLO puede detectar estas cosas:")
    print("👥 Personas, 🚗 Carros, 🐕 Animales, 📱 Teléfonos, 💻 Laptops")
    print("🍎 Comida, ⚽ Deportes, 🪑 Muebles, ✂️ Herramientas, y mucho más!")
    print("\n🔥 ¡Muestra objetos a la cámara para verlos detectados!")
    print("❌ Presiona 'q' para salir")
    print("-" * 60)
    
    # Variables para FPS
    fps_counter = 0
    start_time = time.time()
    
    try:
        while True:
            # Leer frame de la cámara
            ret, frame = cap.read()
            if not ret:
                print("❌ Error leyendo de la cámara")
                break
            
            # Voltear horizontalmente para efecto espejo
            frame = cv2.flip(frame, 1)
            
            # Detectar objetos con YOLO
            results = model(frame, conf=0.3, verbose=False)  # conf=0.3 para mejor detección
            
            # Dibujar detecciones
            if results[0].boxes is not None:
                boxes = results[0].boxes.xyxy.cpu().numpy()
                confidences = results[0].boxes.conf.cpu().numpy()
                classes = results[0].boxes.cls.cpu().numpy()
                
                for box, conf, cls in zip(boxes, confidences, classes):
                    x1, y1, x2, y2 = map(int, box)
                    class_name = model.names[int(cls)]
                    confidence = conf
                    
                    # Colores para diferentes tipos de objetos
                    if class_name == 'person':
                        color = (0, 255, 0)  # Verde para personas
                    elif class_name in ['car', 'truck', 'bus', 'motorcycle']:
                        color = (255, 0, 0)  # Azul para vehículos
                    elif class_name in ['dog', 'cat', 'bird']:
                        color = (0, 255, 255)  # Amarillo para animales
                    elif class_name in ['cell phone', 'laptop', 'tv', 'keyboard']:
                        color = (255, 0, 255)  # Magenta para electrónicos
                    else:
                        color = (0, 165, 255)  # Naranja para otros objetos
                    
                    # Dibujar rectángulo
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    
                    # Preparar etiqueta
                    label = f"{class_name}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    
                    # Fondo para la etiqueta
                    cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                                (x1 + label_size[0], y1), color, -1)
                    
                    # Texto de la etiqueta
                    cv2.putText(frame, label, (x1, y1 - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    
                    # Mostrar en consola los objetos detectados
                    print(f"🎯 Detectado: {class_name} ({confidence:.2f})")
            
            # Calcular y mostrar FPS
            fps_counter += 1
            elapsed_time = time.time() - start_time
            if elapsed_time >= 1.0:
                fps = fps_counter / elapsed_time
                fps_counter = 0
                start_time = time.time()
                
                # Mostrar FPS en la imagen
                cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
                          cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Mostrar información en la imagen
            cv2.putText(frame, "YOLO Deteccion en Tiempo Real", (10, frame.shape[0] - 60), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, "Presiona 'q' para salir", (10, frame.shape[0] - 30), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(frame, f"Modelo: YOLOv8n - {len(model.names)} clases", (10, frame.shape[0] - 10), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # Mostrar frame
            cv2.imshow('YOLO - Deteccion en Tiempo Real', frame)
            
            # Salir con 'q'
            if cv2.waitKey(1) & 0xFF == ord('q'):
                print("\n👋 Cerrando detección en tiempo real...")
                break
                
    except KeyboardInterrupt:
        print("\n⏹️ Detenido por el usuario")
    
    except Exception as e:
        print(f"\n❌ Error: {e}")
    
    finally:
        # Limpiar recursos
        cap.release()
        cv2.destroyAllWindows()
        print("✅ Recursos liberados")
        print("🎉 ¡Gracias por usar YOLO detección en tiempo real!")

if __name__ == "__main__":
    main()
