"""
Servicio de voz para comandos y síntesis
"""

import speech_recognition as sr
import pyttsx3
import whisper
import time
import json
import os
import re
from typing import Dict, Any, List, Optional
import threading

from ..core.config import settings

class VoiceService:
    """Servicio para procesamiento de voz"""
    
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.tts_engine = None
        self.whisper_model = None
        self.setup_services()
    
    def setup_services(self):
        """Configurar servicios de voz"""
        try:
            # Configurar micrófono
            self.microphone = sr.Microphone()
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
            print("✅ Micrófono configurado")
            
            # Configurar TTS
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', settings.VOICE_RATE)
            self.tts_engine.setProperty('volume', settings.VOICE_VOLUME)
            
            # Configurar voz en español si está disponible
            voices = self.tts_engine.getProperty('voices')
            for voice in voices:
                if 'spanish' in voice.name.lower() or 'es' in voice.id.lower():
                    self.tts_engine.setProperty('voice', voice.id)
                    break
            
            print("✅ Motor TTS configurado")
            
            # Cargar modelo Whisper (opcional, para mejor precisión)
            try:
                print("🎤 Cargando modelo Whisper...")
                self.whisper_model = whisper.load_model("base")
                print("✅ Whisper cargado")
            except Exception as e:
                print(f"⚠️ Whisper no disponible: {e}")
                self.whisper_model = None
                
        except Exception as e:
            print(f"❌ Error configurando servicios de voz: {e}")
    
    def speech_to_text_google(self, audio_file_path: str) -> Dict[str, Any]:
        """
        Convertir audio a texto usando Google Speech Recognition
        
        Args:
            audio_file_path: Ruta del archivo de audio
            
        Returns:
            Dict con texto transcrito
        """
        start_time = time.time()
        
        try:
            # Cargar archivo de audio
            with sr.AudioFile(audio_file_path) as source:
                audio = self.recognizer.record(source)
            
            # Transcribir
            text = self.recognizer.recognize_google(
                audio, 
                language=settings.VOICE_LANGUAGE
            )
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "method": "google",
                "transcribed_text": text,
                "confidence": 1.0,  # Google no proporciona confianza
                "processing_time": processing_time,
                "audio_file": audio_file_path
            }
            
        except sr.UnknownValueError:
            return {
                "success": False,
                "method": "google",
                "error": "No se pudo entender el audio",
                "processing_time": time.time() - start_time,
                "audio_file": audio_file_path
            }
        except sr.RequestError as e:
            return {
                "success": False,
                "method": "google",
                "error": f"Error del servicio: {e}",
                "processing_time": time.time() - start_time,
                "audio_file": audio_file_path
            }
    
    def speech_to_text_whisper(self, audio_file_path: str) -> Dict[str, Any]:
        """
        Convertir audio a texto usando Whisper (offline)
        
        Args:
            audio_file_path: Ruta del archivo de audio
            
        Returns:
            Dict con texto transcrito
        """
        if not self.whisper_model:
            return {
                "success": False,
                "method": "whisper",
                "error": "Modelo Whisper no disponible",
                "audio_file": audio_file_path
            }
        
        start_time = time.time()
        
        try:
            # Transcribir con Whisper
            result = self.whisper_model.transcribe(audio_file_path, language="es")
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "method": "whisper",
                "transcribed_text": result["text"].strip(),
                "confidence": 1.0,  # Whisper no proporciona confianza directa
                "segments": result.get("segments", []),
                "processing_time": processing_time,
                "audio_file": audio_file_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "method": "whisper",
                "error": str(e),
                "processing_time": time.time() - start_time,
                "audio_file": audio_file_path
            }
    
    def text_to_speech(self, text: str, output_file: str = None) -> Dict[str, Any]:
        """
        Convertir texto a voz
        
        Args:
            text: Texto a convertir
            output_file: Archivo de salida (opcional)
            
        Returns:
            Dict con resultado
        """
        if not self.tts_engine:
            return {
                "success": False,
                "error": "Motor TTS no disponible"
            }
        
        try:
            if output_file:
                # Guardar en archivo
                self.tts_engine.save_to_file(text, output_file)
                self.tts_engine.runAndWait()
                return {
                    "success": True,
                    "text": text,
                    "output_file": output_file,
                    "method": "file"
                }
            else:
                # Reproducir directamente
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
                return {
                    "success": True,
                    "text": text,
                    "method": "direct"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "text": text
            }
    
    def parse_voice_command(self, text: str) -> Dict[str, Any]:
        """
        Analizar comando de voz y extraer intención
        
        Args:
            text: Texto del comando
            
        Returns:
            Dict con comando parseado
        """
        text = text.lower().strip()
        
        # Patrones de comandos
        command_patterns = {
            "detect_objects": [
                r"detectar objetos",
                r"que ves",
                r"que hay en la imagen",
                r"analizar imagen",
                r"buscar objetos"
            ],
            "detect_text": [
                r"leer texto",
                r"que dice",
                r"extraer texto",
                r"leer imagen",
                r"transcribir"
            ],
            "search_object": [
                r"buscar (.+)",
                r"encontrar (.+)",
                r"hay (.+)",
                r"ver (.+)"
            ],
            "count_objects": [
                r"contar (.+)",
                r"cuantos (.+)",
                r"numero de (.+)"
            ],
            "help": [
                r"ayuda",
                r"que puedes hacer",
                r"comandos",
                r"opciones"
            ]
        }
        
        # Buscar patrón coincidente
        for command_type, patterns in command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    result = {
                        "command_type": command_type,
                        "original_text": text,
                        "confidence": 1.0
                    }
                    
                    # Extraer parámetros si los hay
                    if match.groups():
                        result["parameters"] = {
                            "target": match.group(1).strip()
                        }
                    
                    return result
        
        # Si no se encuentra patrón específico
        return {
            "command_type": "unknown",
            "original_text": text,
            "confidence": 0.0,
            "suggestion": "Intenta comandos como: 'detectar objetos', 'leer texto', 'buscar [objeto]'"
        }
    
    def process_voice_command(self, audio_file_path: str) -> Dict[str, Any]:
        """
        Procesar comando de voz completo
        
        Args:
            audio_file_path: Ruta del archivo de audio
            
        Returns:
            Dict con comando procesado
        """
        start_time = time.time()
        
        # Transcribir audio
        transcription = self.speech_to_text_whisper(audio_file_path)
        if not transcription["success"]:
            transcription = self.speech_to_text_google(audio_file_path)
        
        if not transcription["success"]:
            return {
                "success": False,
                "error": "No se pudo transcribir el audio",
                "processing_time": time.time() - start_time
            }
        
        # Parsear comando
        command = self.parse_voice_command(transcription["transcribed_text"])
        
        # Combinar resultados
        result = {
            "success": True,
            "transcription": transcription,
            "command": command,
            "processing_time": time.time() - start_time,
            "audio_file": audio_file_path
        }
        
        return result
    
    def get_response_text(self, command_type: str, results: Dict[str, Any]) -> str:
        """
        Generar respuesta de texto basada en el comando y resultados
        
        Args:
            command_type: Tipo de comando
            results: Resultados de la operación
            
        Returns:
            Texto de respuesta
        """
        if command_type == "detect_objects":
            if results.get("total_objects", 0) > 0:
                objects = [det["class_name"] for det in results.get("detections", [])]
                unique_objects = list(set(objects))
                return f"He detectado {len(objects)} objetos: {', '.join(unique_objects[:5])}"
            else:
                return "No he detectado ningún objeto en la imagen"
        
        elif command_type == "detect_text":
            text = results.get("full_text", "").strip()
            if text:
                return f"El texto dice: {text[:100]}..."
            else:
                return "No he encontrado texto en la imagen"
        
        elif command_type == "search_object":
            target = results.get("search_targets", ["objeto"])[0]
            found = results.get("total_objects", 0)
            if found > 0:
                return f"He encontrado {found} {target}(s) en la imagen"
            else:
                return f"No he encontrado {target} en la imagen"
        
        elif command_type == "count_objects":
            count = results.get("total_objects", 0)
            return f"He contado {count} objetos en total"
        
        elif command_type == "help":
            return "Puedo detectar objetos, leer texto, buscar elementos específicos y contar objetos. ¿Qué necesitas?"
        
        else:
            return "No he entendido el comando. Intenta con 'detectar objetos' o 'leer texto'"
