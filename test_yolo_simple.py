"""
Prueba simple de YOLO
"""

print("🔍 Probando YOLO...")

try:
    print("📦 Importando ultralytics...")
    from ultralytics import YOLO
    print("✅ Ultralytics importado")
    
    print("🤖 Cargando modelo YOLO...")
    model = YOLO('yolov8n.pt')  # Esto descargará el modelo si no existe
    print("✅ Modelo YOLO cargado")
    
    print("🏷️ Clases disponibles:")
    if hasattr(model, 'names'):
        print(f"Total: {len(model.names)}")
        print(f"Primeras 10: {list(model.names.values())[:10]}")
    
    print("✅ YOLO funcionando correctamente!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
