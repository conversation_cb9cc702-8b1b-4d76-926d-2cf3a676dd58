"""
Entrenador de YOLOv11 personalizado para medicamentos
"""

import os
import yaml
from ultralytics import YOLO
import shutil
from datetime import datetime

def create_dataset_yaml(dataset_path, classes):
    """Crear archivo dataset.yaml para YOLO"""
    yaml_content = {
        'path': os.path.abspath(dataset_path),
        'train': 'images/train',
        'val': 'images/val', 
        'test': 'images/test',
        'nc': len(classes),
        'names': classes
    }
    
    yaml_path = os.path.join(dataset_path, 'dataset.yaml')
    with open(yaml_path, 'w') as f:
        yaml.dump(yaml_content, f, default_flow_style=False)
    
    return yaml_path

def setup_medicamentos_training():
    """Configurar entrenamiento para medicamentos"""
    print("🤖 Configurando Entrenamiento YOLOv11 para Medicamentos")
    print("=" * 60)
    
    dataset_path = "datasets/manual_medicamentos"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset no encontrado: {dataset_path}")
        return None
    
    # Verificar imágenes
    raw_images_path = os.path.join(dataset_path, "raw_images")
    if not os.path.exists(raw_images_path):
        print(f"❌ Carpeta de imágenes no encontrada: {raw_images_path}")
        return None
    
    image_files = [f for f in os.listdir(raw_images_path) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    print(f"📊 Imágenes disponibles: {len(image_files)}")
    
    if len(image_files) < 20:
        print("⚠️ Pocas imágenes para entrenar. Recomendado: 50+")
    
    # Definir clases de medicamentos basadas en las imágenes
    medicamento_classes = [
        'A-ferin',
        'Apranax', 
        'Arveles',
        'Aspirin',
        'Dikloron',
        'Dolorex',
        'HametanKrem',
        'HametanMerhem',
        'Majezik',
        'Metpamid',
        'Parol',
        'Terbisil',
        'Unisom',
        'pastilla_generica'  # Para pastillas no identificadas
    ]
    
    print(f"🏷️ Clases de medicamentos: {len(medicamento_classes)}")
    for i, clase in enumerate(medicamento_classes):
        print(f"   {i}: {clase}")
    
    # Crear archivo dataset.yaml
    yaml_path = create_dataset_yaml(dataset_path, medicamento_classes)
    print(f"📄 Dataset YAML creado: {yaml_path}")
    
    return dataset_path, yaml_path, medicamento_classes

def train_yolo11_medicamentos():
    """Entrenar modelo YOLOv11 para medicamentos"""
    print("🚀 Iniciando Entrenamiento YOLOv11 para Medicamentos")
    print("=" * 60)
    
    # Configurar dataset
    setup_result = setup_medicamentos_training()
    if not setup_result:
        return
    
    dataset_path, yaml_path, classes = setup_result
    
    print("\n⚠️ IMPORTANTE:")
    print("📋 Antes de entrenar necesitas:")
    print("1. 🌐 Anotar las imágenes en Roboflow.com")
    print("2. 📥 Descargar dataset anotado en formato YOLOv8")
    print("3. 📂 Extraer en la carpeta del dataset")
    print("4. ✅ Verificar que existan carpetas images/ y labels/")
    
    # Verificar si el dataset está anotado
    images_train = os.path.join(dataset_path, "images", "train")
    labels_train = os.path.join(dataset_path, "labels", "train")
    
    if not (os.path.exists(images_train) and os.path.exists(labels_train)):
        print("\n❌ Dataset no está anotado aún")
        print("🎯 Pasos para anotar:")
        print("1. Ve a: https://roboflow.com/")
        print("2. Crea proyecto nuevo")
        print("3. Sube imágenes de: raw_images/")
        print("4. Anota cada medicamento con bounding boxes")
        print("5. Exporta en formato 'YOLOv8'")
        print("6. Descarga y extrae en esta carpeta")
        print("7. Ejecuta este script de nuevo")
        return
    
    # Contar imágenes anotadas
    train_images = len([f for f in os.listdir(images_train) 
                       if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    train_labels = len([f for f in os.listdir(labels_train) 
                       if f.endswith('.txt')])
    
    print(f"\n📊 Dataset anotado encontrado:")
    print(f"   📸 Imágenes de entrenamiento: {train_images}")
    print(f"   🏷️ Etiquetas de entrenamiento: {train_labels}")
    
    if train_images != train_labels:
        print("⚠️ Número de imágenes y etiquetas no coincide")
    
    if train_images < 20:
        print("⚠️ Pocas imágenes para entrenar bien")
        print("💡 Recomendado: 50+ imágenes por clase")
    
    # Confirmar entrenamiento
    print(f"\n🎯 ¿Iniciar entrenamiento YOLOv11?")
    print(f"📊 Clases: {len(classes)} tipos de medicamentos")
    print(f"📸 Imágenes: {train_images}")
    print(f"⏱️ Tiempo estimado: 30-60 minutos")
    
    confirm = input("\n¿Continuar? (s/n): ").lower().strip()
    if not confirm.startswith('s'):
        print("❌ Entrenamiento cancelado")
        return
    
    try:
        # Cargar modelo YOLOv11
        print("\n🤖 Cargando YOLOv11...")
        model = YOLO('yolo11n.pt')  # YOLOv11 nano (más rápido)
        
        # Configurar entrenamiento
        print("⚙️ Configurando entrenamiento...")
        
        # Parámetros de entrenamiento
        epochs = 100  # Número de épocas
        batch_size = 16  # Tamaño de lote
        img_size = 640  # Tamaño de imagen
        
        print(f"📋 Parámetros:")
        print(f"   🔄 Épocas: {epochs}")
        print(f"   📦 Batch size: {batch_size}")
        print(f"   📐 Tamaño imagen: {img_size}")
        
        # Iniciar entrenamiento
        print(f"\n🚀 Iniciando entrenamiento...")
        print(f"⏱️ Esto puede tomar 30-60 minutos...")
        
        results = model.train(
            data=yaml_path,
            epochs=epochs,
            batch=batch_size,
            imgsz=img_size,
            device='cpu',  # Usar CPU (cambiar a 'cuda' si tienes GPU)
            project='runs/train',
            name='medicamentos_yolo11',
            save=True,
            plots=True,
            verbose=True
        )
        
        print("\n🎉 ¡Entrenamiento completado!")
        
        # Información del modelo entrenado
        model_path = results.save_dir / 'weights' / 'best.pt'
        print(f"💾 Modelo guardado en: {model_path}")
        
        # Crear script de prueba
        create_test_script(model_path, classes)
        
        print(f"\n📊 Resultados del entrenamiento:")
        print(f"📁 Carpeta: {results.save_dir}")
        print(f"📈 Gráficas: {results.save_dir}/results.png")
        print(f"🎯 Modelo: {model_path}")
        
        print(f"\n🎯 Próximos pasos:")
        print(f"1. 🧪 Probar modelo: python test_medicamentos_trained.py")
        print(f"2. 📊 Ver métricas en: {results.save_dir}")
        print(f"3. 🎥 Usar en tiempo real: python yolo_medicamentos_live.py")
        
    except Exception as e:
        print(f"❌ Error durante entrenamiento: {e}")
        print("💡 Posibles soluciones:")
        print("   - Verificar que el dataset esté bien anotado")
        print("   - Reducir batch_size si hay problemas de memoria")
        print("   - Verificar que todas las imágenes sean válidas")

def create_test_script(model_path, classes):
    """Crear script para probar el modelo entrenado"""
    test_script = f'''"""
Probador del modelo YOLOv11 entrenado para medicamentos
"""

import cv2
import os
from ultralytics import YOLO

def test_trained_model():
    """Probar modelo entrenado"""
    print("🧪 Probando Modelo YOLOv11 Entrenado para Medicamentos")
    print("=" * 60)
    
    # Cargar modelo entrenado
    model_path = "{model_path}"
    if not os.path.exists(model_path):
        print(f"❌ Modelo no encontrado: {{model_path}}")
        return
    
    print(f"🤖 Cargando modelo entrenado...")
    model = YOLO(model_path)
    
    # Probar con imágenes del dataset
    test_dir = "datasets/manual_medicamentos/raw_images"
    image_files = [f for f in os.listdir(test_dir) if f.endswith('.jpg')][:3]
    
    for img_file in image_files:
        print(f"\\n📸 Probando: {{img_file}}")
        
        img_path = os.path.join(test_dir, img_file)
        image = cv2.imread(img_path)
        
        # Redimensionar
        height, width = image.shape[:2]
        if width > 800:
            scale = 800 / width
            image = cv2.resize(image, (800, int(height * scale)))
        
        # Detectar
        results = model(image)
        
        # Mostrar resultados
        for result in results:
            if result.boxes is not None and len(result.boxes) > 0:
                print(f"   ✅ {{len(result.boxes)}} medicamentos detectados:")
                
                for box in result.boxes:
                    conf = float(box.conf[0])
                    cls = int(box.cls[0])
                    class_name = model.names[cls]
                    
                    print(f"      💊 {{class_name}}: {{conf:.2f}} confianza")
                    
                    # Dibujar
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(image, f"{{class_name}}: {{conf:.2f}}", 
                              (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            else:
                print("   ❌ No se detectaron medicamentos")
        
        # Mostrar imagen
        cv2.imshow(f"Medicamentos Detectados - {{img_file}}", image)
        cv2.waitKey(2000)  # 2 segundos
        cv2.destroyAllWindows()
    
    print("\\n🎉 ¡Prueba completada!")
    print("💊 Tu modelo personalizado puede detectar medicamentos específicos")

if __name__ == "__main__":
    test_trained_model()
'''
    
    with open("test_medicamentos_trained.py", 'w') as f:
        f.write(test_script)
    
    print("📄 Script de prueba creado: test_medicamentos_trained.py")

def main():
    """Función principal"""
    print("🤖 Entrenador YOLOv11 para Medicamentos")
    print("Crear modelo personalizado para detectar pastillas específicas")
    
    while True:
        print("\n📋 Opciones:")
        print("1. ⚙️ Configurar dataset")
        print("2. 🚀 Entrenar modelo YOLOv11")
        print("3. 📋 Ver estado del dataset")
        print("4. ❌ Salir")
        
        choice = input("\nElige opción (1-4): ").strip()
        
        if choice == '1':
            setup_medicamentos_training()
        elif choice == '2':
            train_yolo11_medicamentos()
        elif choice == '3':
            # Mostrar estado
            dataset_path = "datasets/manual_medicamentos"
            raw_images = len([f for f in os.listdir(f"{dataset_path}/raw_images") 
                            if f.endswith('.jpg')]) if os.path.exists(f"{dataset_path}/raw_images") else 0
            
            train_path = f"{dataset_path}/images/train"
            train_images = len([f for f in os.listdir(train_path) 
                              if f.endswith('.jpg')]) if os.path.exists(train_path) else 0
            
            print(f"\n📊 Estado del Dataset:")
            print(f"📸 Imágenes sin anotar: {raw_images}")
            print(f"✅ Imágenes anotadas: {train_images}")
            
            if train_images > 0:
                print("🎯 ¡Listo para entrenar!")
            else:
                print("⚠️ Necesita anotación en Roboflow")
                
        elif choice == '4':
            print("👋 ¡Hasta luego!")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
