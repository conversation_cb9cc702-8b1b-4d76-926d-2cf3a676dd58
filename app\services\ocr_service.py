"""
Servicio OCR para reconocimiento de texto
Detecta letras, números, texto en general
"""

import cv2
import numpy as np
import easyocr
import pytesseract
from PIL import Image
import time
import json
import re
from typing import List, Dict, Any, Tuple
import os

from ..core.config import settings

class OCRService:
    """Servicio para reconocimiento de texto (OCR)"""
    
    def __init__(self):
        self.easyocr_reader = None
        self.languages = settings.OCR_LANGUAGES
        self.gpu = settings.OCR_GPU
        self.load_ocr_models()
    
    def load_ocr_models(self):
        """Cargar modelos OCR"""
        try:
            print(f"📖 Cargando EasyOCR con idiomas: {self.languages}")
            self.easyocr_reader = easyocr.Reader(
                self.languages, 
                gpu=self.gpu,
                verbose=False
            )
            print("✅ EasyOCR cargado correctamente")
            
            # Verificar Tesseract
            try:
                pytesseract.get_tesseract_version()
                print("✅ Tesseract disponible")
            except:
                print("⚠️ Tesseract no disponible")
                
        except Exception as e:
            print(f"❌ Error cargando OCR: {e}")
            self.easyocr_reader = None
    
    def extract_text_easyocr(self, image_path: str) -> Dict[str, Any]:
        """
        Extraer texto usando EasyOCR
        
        Args:
            image_path: Ruta de la imagen
            
        Returns:
            Dict con texto extraído y coordenadas
        """
        if not self.easyocr_reader:
            raise Exception("EasyOCR no está disponible")
        
        start_time = time.time()
        
        try:
            # Leer imagen
            image = cv2.imread(image_path)
            if image is None:
                raise Exception(f"No se pudo cargar la imagen: {image_path}")
            
            # Extraer texto
            results = self.easyocr_reader.readtext(image)
            
            # Procesar resultados
            text_detections = []
            full_text = ""
            
            for (bbox, text, confidence) in results:
                # Coordenadas del bounding box
                x1, y1 = bbox[0]
                x2, y2 = bbox[2]
                
                detection = {
                    "text": text,
                    "confidence": float(confidence),
                    "bbox": {
                        "x1": float(x1),
                        "y1": float(y1),
                        "x2": float(x2),
                        "y2": float(y2),
                        "width": float(x2 - x1),
                        "height": float(y2 - y1)
                    }
                }
                text_detections.append(detection)
                full_text += text + " "
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "method": "easyocr",
                "image_path": image_path,
                "text_detections": text_detections,
                "full_text": full_text.strip(),
                "total_text_blocks": len(text_detections),
                "processing_time": processing_time
            }
            
        except Exception as e:
            return {
                "success": False,
                "method": "easyocr",
                "error": str(e),
                "image_path": image_path,
                "processing_time": time.time() - start_time
            }
    
    def extract_text_tesseract(self, image_path: str) -> Dict[str, Any]:
        """
        Extraer texto usando Tesseract
        
        Args:
            image_path: Ruta de la imagen
            
        Returns:
            Dict con texto extraído
        """
        start_time = time.time()
        
        try:
            # Cargar imagen
            image = Image.open(image_path)
            
            # Configuración de Tesseract
            config = '--oem 3 --psm 6'  # OCR Engine Mode 3, Page Segmentation Mode 6
            
            # Extraer texto
            text = pytesseract.image_to_string(image, config=config, lang='spa+eng')
            
            # Obtener datos detallados
            data = pytesseract.image_to_data(image, config=config, lang='spa+eng', output_type=pytesseract.Output.DICT)
            
            # Procesar datos detallados
            text_detections = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > 0:  # Solo texto con confianza > 0
                    detection = {
                        "text": data['text'][i],
                        "confidence": float(data['conf'][i]) / 100.0,  # Normalizar a 0-1
                        "bbox": {
                            "x1": float(data['left'][i]),
                            "y1": float(data['top'][i]),
                            "x2": float(data['left'][i] + data['width'][i]),
                            "y2": float(data['top'][i] + data['height'][i]),
                            "width": float(data['width'][i]),
                            "height": float(data['height'][i])
                        }
                    }
                    text_detections.append(detection)
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "method": "tesseract",
                "image_path": image_path,
                "text_detections": text_detections,
                "full_text": text.strip(),
                "total_text_blocks": len(text_detections),
                "processing_time": processing_time
            }
            
        except Exception as e:
            return {
                "success": False,
                "method": "tesseract",
                "error": str(e),
                "image_path": image_path,
                "processing_time": time.time() - start_time
            }
    
    def extract_text_combined(self, image_path: str) -> Dict[str, Any]:
        """
        Extraer texto usando ambos métodos y combinar resultados
        
        Args:
            image_path: Ruta de la imagen
            
        Returns:
            Dict con resultados combinados
        """
        start_time = time.time()
        
        # Ejecutar ambos métodos
        easyocr_results = self.extract_text_easyocr(image_path)
        tesseract_results = self.extract_text_tesseract(image_path)
        
        # Combinar resultados
        combined_text = ""
        all_detections = []
        
        if easyocr_results["success"]:
            all_detections.extend(easyocr_results["text_detections"])
            combined_text += easyocr_results["full_text"] + " "
        
        if tesseract_results["success"]:
            all_detections.extend(tesseract_results["text_detections"])
            combined_text += tesseract_results["full_text"] + " "
        
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "method": "combined",
            "image_path": image_path,
            "easyocr_results": easyocr_results,
            "tesseract_results": tesseract_results,
            "combined_text": combined_text.strip(),
            "all_detections": all_detections,
            "total_text_blocks": len(all_detections),
            "processing_time": processing_time
        }
    
    def detect_specific_patterns(self, text: str) -> Dict[str, List[str]]:
        """
        Detectar patrones específicos en el texto
        
        Args:
            text: Texto a analizar
            
        Returns:
            Dict con patrones encontrados
        """
        patterns = {
            "emails": re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text),
            "phones": re.findall(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', text),
            "numbers": re.findall(r'\b\d+\b', text),
            "dates": re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', text),
            "urls": re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text),
            "medicines": self._detect_medicine_names(text),
            "dosages": re.findall(r'\b\d+\s*mg\b|\b\d+\s*ml\b|\b\d+\s*g\b', text, re.IGNORECASE)
        }
        
        return patterns
    
    def _detect_medicine_names(self, text: str) -> List[str]:
        """Detectar nombres de medicamentos comunes"""
        # Lista básica de medicamentos comunes
        common_medicines = [
            "paracetamol", "ibuprofeno", "aspirina", "amoxicilina", "omeprazol",
            "simvastatina", "metformina", "losartan", "atorvastatina", "diclofenaco"
        ]
        
        found_medicines = []
        text_lower = text.lower()
        
        for medicine in common_medicines:
            if medicine in text_lower:
                found_medicines.append(medicine)
        
        return found_medicines
    
    def extract_and_annotate(self, image_path: str, output_path: str = None) -> Dict[str, Any]:
        """
        Extraer texto y crear imagen anotada
        
        Args:
            image_path: Ruta de imagen original
            output_path: Ruta para guardar imagen anotada
            
        Returns:
            Dict con resultados y ruta de imagen anotada
        """
        # Extraer texto
        results = self.extract_text_easyocr(image_path)
        
        if not results["success"]:
            return results
        
        try:
            # Cargar imagen
            image = cv2.imread(image_path)
            annotated_image = image.copy()
            
            # Dibujar detecciones de texto
            for detection in results["text_detections"]:
                bbox = detection["bbox"]
                x1, y1, x2, y2 = int(bbox["x1"]), int(bbox["y1"]), int(bbox["x2"]), int(bbox["y2"])
                
                # Dibujar rectángulo
                cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
                
                # Dibujar texto detectado
                text = detection["text"][:20] + "..." if len(detection["text"]) > 20 else detection["text"]
                cv2.putText(annotated_image, text, (x1, y1 - 5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            # Guardar imagen anotada
            if not output_path:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = os.path.join(settings.TEMP_DIR, f"{base_name}_ocr_annotated.jpg")
            
            cv2.imwrite(output_path, annotated_image)
            
            results["annotated_image_path"] = output_path
            return results
            
        except Exception as e:
            results["annotation_error"] = str(e)
            return results
