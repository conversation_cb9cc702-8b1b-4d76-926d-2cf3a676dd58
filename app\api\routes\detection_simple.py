"""
Rutas simplificadas para detección de objetos (solo YOLO)
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import shutil
import uuid
from datetime import datetime

from ...core.config import settings
from ...core.database import get_db, DetectionResult
from ...services.yolo_service import YOLOService

# Función para obtener servicio YOLO
def get_yolo_service():
    from main import yolo_service
    if yolo_service is None:
        raise HTTPException(status_code=503, detail="YOLO service not available")
    return yolo_service
from sqlalchemy.orm import Session
import json

router = APIRouter()

def save_uploaded_file(upload_file: UploadFile, directory: str) -> str:
    """Guardar archivo subido"""
    # Generar nombre único
    file_extension = os.path.splitext(upload_file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(directory, unique_filename)

    # Guardar archivo
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)

    return file_path

@router.post("/objects")
async def detect_objects(
    image: UploadFile = File(...),
    confidence: Optional[float] = None,
    annotate: bool = False,
    yolo_service: YOLOService = Depends(get_yolo_service),
    db: Session = Depends(get_db)
):
    """
    Detectar objetos en una imagen usando YOLO

    Args:
        image: Archivo de imagen
        confidence: Umbral de confianza (opcional)
        annotate: Si crear imagen anotada

    Returns:
        Resultados de detección
    """
    # Validar archivo
    if not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    if image.size > settings.MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="Archivo demasiado grande")

    try:
        # Guardar imagen
        image_path = save_uploaded_file(image, settings.UPLOAD_DIR)

        # Ajustar confianza si se proporciona
        if confidence:
            original_confidence = yolo_service.confidence
            yolo_service.confidence = confidence

        # Detectar objetos
        if annotate:
            results = yolo_service.detect_and_annotate(image_path)
        else:
            results = yolo_service.detect_objects(image_path)

        # Restaurar confianza original
        if confidence:
            yolo_service.confidence = original_confidence

        # Guardar en base de datos
        db_result = DetectionResult(
            image_path=image_path,
            detection_type="yolo",
            objects_detected=json.dumps(results.get("detections", [])),
            confidence_scores=json.dumps([det.get("confidence", 0) for det in results.get("detections", [])]),
            processing_time=results.get("processing_time", 0)
        )
        db.add(db_result)
        db.commit()

        # Agregar ID de base de datos a la respuesta
        results["db_id"] = db_result.id

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error procesando imagen: {str(e)}")

@router.post("/search")
async def search_objects(
    image: UploadFile = File(...),
    targets: str = Form(...),  # Lista separada por comas
    yolo_service: YOLOService = Depends(get_yolo_service),
    db: Session = Depends(get_db)
):
    """
    Buscar objetos específicos en una imagen

    Args:
        image: Archivo de imagen
        targets: Objetos a buscar (separados por comas)

    Returns:
        Objetos encontrados
    """
    # Validar archivo
    if not image.filename.lower().endswith(tuple(settings.ALLOWED_IMAGE_EXTENSIONS)):
        raise HTTPException(status_code=400, detail="Formato de imagen no válido")

    try:
        # Guardar imagen
        image_path = save_uploaded_file(image, settings.UPLOAD_DIR)

        # Procesar lista de objetivos
        target_list = [target.strip() for target in targets.split(",")]

        # Buscar objetos
        results = yolo_service.search_objects(image_path, target_list)

        # Guardar en base de datos
        db_result = DetectionResult(
            image_path=image_path,
            detection_type="search",
            objects_detected=json.dumps(results.get("detections", [])),
            confidence_scores=json.dumps([det.get("confidence", 0) for det in results.get("detections", [])]),
            processing_time=results.get("processing_time", 0)
        )
        db.add(db_result)
        db.commit()

        results["db_id"] = db_result.id

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error buscando objetos: {str(e)}")

@router.get("/classes")
async def get_available_classes(yolo_service: YOLOService = Depends(get_yolo_service)):
    """
    Obtener lista de clases que puede detectar YOLO

    Returns:
        Lista de clases disponibles
    """
    try:
        classes = yolo_service.get_available_classes()
        return {
            "success": True,
            "total_classes": len(classes),
            "classes": classes
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo clases: {str(e)}")

@router.get("/image/{filename}")
async def get_image(filename: str):
    """
    Obtener imagen procesada

    Args:
        filename: Nombre del archivo

    Returns:
        Archivo de imagen
    """
    file_path = os.path.join(settings.TEMP_DIR, filename)
    if not os.path.exists(file_path):
        # Buscar en directorio de uploads también
        file_path = os.path.join(settings.UPLOAD_DIR, filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Imagen no encontrada")

    return FileResponse(file_path)

@router.get("/history")
async def get_detection_history(
    limit: int = 10,
    detection_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Obtener historial de detecciones

    Args:
        limit: Número máximo de resultados
        detection_type: Filtrar por tipo de detección

    Returns:
        Historial de detecciones
    """
    try:
        query = db.query(DetectionResult)

        if detection_type:
            query = query.filter(DetectionResult.detection_type == detection_type)

        results = query.order_by(DetectionResult.created_at.desc()).limit(limit).all()

        history = []
        for result in results:
            history.append({
                "id": result.id,
                "image_path": result.image_path,
                "detection_type": result.detection_type,
                "processing_time": result.processing_time,
                "created_at": result.created_at.isoformat(),
                "objects_count": len(json.loads(result.objects_detected)) if result.objects_detected else 0
            })

        return {
            "success": True,
            "total_results": len(history),
            "history": history
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo historial: {str(e)}")
